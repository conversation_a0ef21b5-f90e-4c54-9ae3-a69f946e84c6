#!/bin/bash

# University of Gondar Event Management System - Local Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}UoG Event Management - Local Deployment${NC}"
echo -e "${BLUE}========================================${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Error: Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo -e "${RED}Error: Docker Compose is not installed.${NC}"
    exit 1
fi

# Load environment variables
if [ -f .env.local ]; then
    echo -e "${YELLOW}Loading local environment variables...${NC}"
    export $(cat .env.local | grep -v '^#' | xargs)
else
    echo -e "${YELLOW}Creating .env.local from template...${NC}"
    cp .env.local.template .env.local
    echo -e "${YELLOW}Please edit .env.local with your configuration and run this script again.${NC}"
    exit 1
fi

# Stop existing containers
echo -e "${YELLOW}Stopping existing containers...${NC}"
docker-compose -f docker-compose.dev.yml down

# Remove old images (optional)
read -p "Do you want to rebuild images? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Removing old images...${NC}"
    docker-compose -f docker-compose.dev.yml build --no-cache
else
    echo -e "${YELLOW}Building images...${NC}"
    docker-compose -f docker-compose.dev.yml build
fi

# Start services
echo -e "${YELLOW}Starting services...${NC}"
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be ready
echo -e "${YELLOW}Waiting for services to be ready...${NC}"
sleep 10

# Check service health
echo -e "${YELLOW}Checking service health...${NC}"

# Check database
if docker-compose -f docker-compose.dev.yml exec -T db pg_isready -U postgres > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Database is ready${NC}"
else
    echo -e "${RED}✗ Database is not ready${NC}"
fi

# Check Redis
if docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Redis is ready${NC}"
else
    echo -e "${RED}✗ Redis is not ready${NC}"
fi

# Check backend
if curl -f http://localhost:8000/admin/login/ > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Backend is ready${NC}"
else
    echo -e "${RED}✗ Backend is not ready${NC}"
fi

# Check frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Frontend is ready${NC}"
else
    echo -e "${RED}✗ Frontend is not ready${NC}"
fi

echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}Local deployment completed!${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${YELLOW}Services:${NC}"
echo -e "Frontend: ${BLUE}http://localhost:3000${NC}"
echo -e "Backend API: ${BLUE}http://localhost:8000/api${NC}"
echo -e "Admin Panel: ${BLUE}http://localhost:8000/admin${NC}"
echo -e "Database: ${BLUE}localhost:5432${NC}"
echo -e "Redis: ${BLUE}localhost:6379${NC}"
echo -e "${BLUE}========================================${NC}"

# Show logs
read -p "Do you want to view logs? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    docker-compose -f docker-compose.dev.yml logs -f
fi

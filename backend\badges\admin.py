from django.contrib import admin
from .models import Badge, BadgeTemplate


@admin.register(BadgeTemplate)
class BadgeTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'width', 'height', 'background_color', 'is_default', 'created_at']
    list_filter = ['is_default', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Badge)
class BadgeAdmin(admin.ModelAdmin):
    list_display = ['participant', 'template', 'is_generated', 'generated_at', 'created_at']
    list_filter = ['is_generated', 'template', 'generated_at', 'created_at']
    search_fields = ['participant__first_name', 'participant__last_name']
    readonly_fields = ['qr_code_data', 'created_at', 'updated_at']

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields + ['participant']
        return self.readonly_fields

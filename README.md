# Event Management System

A comprehensive professional event management system built with Django (backend) and <PERSON>act (frontend). This system provides participant registration, badge generation with QR codes, attendance tracking, event scheduling, gallery management, and more.

## Features

### 🎯 Core Features
- **Participant Registration**: Complete registration system with photo upload
- **Professional Badge Generation**: Automatic badge creation with QR codes and participant photos
- **QR Code Attendance Tracking**: Scan QR codes to track attendance at sessions
- **Event Scheduling**: Daily schedule management with session details
- **Event Gallery**: Photo sharing and download functionality
- **Hotel & Driver Management**: Integrated accommodation and transportation management
- **Multi-participant Types**: Color-coded participant categories

### 🔧 Technical Features
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **RESTful API**: Complete REST API for all functionality
- **Docker Support**: Full containerization for easy deployment
- **Environment Configuration**: Proper environment variable management
- **Professional UI**: Bootstrap-based responsive interface
- **File Upload**: Secure image upload and management
- **Database Support**: PostgreSQL for production, SQLite for development

## Technology Stack

### Backend
- **Django 4.2.7**: Python web framework
- **Django REST Framework**: API development
- **PostgreSQL**: Production database
- **SQLite**: Development database
- **Pillow**: Image processing
- **QRCode**: QR code generation
- **ReportLab**: PDF generation for badges
- **Gunicorn**: WSGI server
- **WhiteNoise**: Static file serving

### Frontend
- **React 18**: JavaScript framework
- **TypeScript**: Type-safe JavaScript
- **React Router**: Client-side routing
- **Bootstrap 5**: CSS framework
- **Axios**: HTTP client
- **React DatePicker**: Date/time selection
- **Font Awesome**: Icons

### DevOps
- **Docker**: Containerization
- **Docker Compose**: Multi-container orchestration
- **Nginx**: Reverse proxy and static file serving
- **Redis**: Caching and session storage

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Git

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd uog-event
   ```

2. **Copy environment files**
   ```bash
   cp .env.example .env
   cp .env.dev .env.local
   ```

3. **Start development environment**
   ```bash
   docker-compose -f docker-compose.dev.yml up --build
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000/api
   - Admin Panel: http://localhost:8000/admin

5. **Create superuser (in a new terminal)**
   ```bash
   docker-compose -f docker-compose.dev.yml exec backend python manage.py createsuperuser
   ```

### Production Setup

1. **Update environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with production values
   ```

2. **Start production environment**
   ```bash
   docker-compose up --build -d
   ```

3. **Run migrations and collect static files**
   ```bash
   docker-compose exec backend python manage.py migrate
   docker-compose exec backend python manage.py collectstatic --noinput
   docker-compose exec backend python manage.py createsuperuser
   ```

## Manual Setup (Without Docker)

### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

5. **Run migrations**
   ```bash
   python manage.py migrate
   python manage.py createsuperuser
   ```

6. **Start development server**
   ```bash
   python manage.py runserver
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

4. **Start development server**
   ```bash
   npm start
   ```

## API Documentation

### Main Endpoints

- **Events**: `/api/events/`
- **Participants**: `/api/participants/`
- **Participant Types**: `/api/participant-types/`
- **Event Schedules**: `/api/schedules/`
- **Event Gallery**: `/api/gallery/`
- **Attendance**: `/api/attendance/`

### Key API Features

- **Participant Registration**: `POST /api/participants/`
- **Badge Generation**: Automatic on participant creation
- **QR Code Verification**: `GET /api/participants/verify/?uuid=<uuid>`
- **Attendance Check-in**: `POST /api/attendance/check_in/`

## Environment Variables

### Backend (.env)
```env
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=localhost,yourdomain.com
DATABASE_URL=postgresql://user:pass@localhost/dbname
CORS_ALLOWED_ORIGINS=http://localhost:3000
QR_CODE_BASE_URL=http://localhost:8000
```

### Frontend (.env)
```env
REACT_APP_API_BASE_URL=http://localhost:8000/api
REACT_APP_BACKEND_URL=http://localhost:8000
```

## Usage

### Admin Panel
1. Access admin panel at `/admin`
2. Create events, participant types, and manage data
3. View participant registrations and generate reports

### Participant Registration
1. Navigate to `/register`
2. Fill in participant details
3. Upload profile photo for badge
4. Select event and participant type
5. Submit registration

### Badge Generation
- Badges are automatically generated upon registration
- Include participant photo, QR code, and event details
- Color-coded by participant type
- Professional vertical format

### QR Code Attendance
1. Use QR scanner at `/scan`
2. Scan participant badges
3. Automatic attendance recording
4. Real-time attendance tracking

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please open an issue in the repository or contact the development team.

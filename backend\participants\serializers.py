from rest_framework import serializers
from .models import Participant, ParticipantType, Attendance, VisitingInterest, ParticipantVisitingInterest


class ParticipantTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ParticipantType
        fields = '__all__'


class VisitingInterestSerializer(serializers.ModelSerializer):
    current_participants_count = serializers.ReadOnlyField()
    is_full = serializers.ReadOnlyField()

    class Meta:
        model = VisitingInterest
        fields = '__all__'


class ParticipantVisitingInterestSerializer(serializers.ModelSerializer):
    visiting_interest_name = serializers.CharField(source='visiting_interest.name', read_only=True)
    visiting_interest_description = serializers.CharField(source='visiting_interest.description', read_only=True)
    visiting_interest_location = serializers.CharField(source='visiting_interest.location', read_only=True)

    class Meta:
        model = ParticipantVisitingInterest
        fields = ['id', 'visiting_interest', 'visiting_interest_name', 'visiting_interest_description',
                 'visiting_interest_location', 'priority', 'selected_at']


class AttendanceSerializer(serializers.ModelSerializer):
    participant_name = serializers.CharField(source='participant.full_name', read_only=True)
    event_title = serializers.CharField(source='event_schedule.title', read_only=True)

    class Meta:
        model = Attendance
        fields = '__all__'


class ParticipantSerializer(serializers.ModelSerializer):
    full_name = serializers.CharField(read_only=True)
    participant_type_name = serializers.CharField(source='participant_type.name', read_only=True)
    participant_type_color = serializers.CharField(source='participant_type.color', read_only=True)
    event_name = serializers.CharField(source='event.name', read_only=True)
    attendances = AttendanceSerializer(many=True, read_only=True)
    visiting_interests = ParticipantVisitingInterestSerializer(many=True, read_only=True)
    badge_url = serializers.SerializerMethodField()

    class Meta:
        model = Participant
        fields = '__all__'

    def get_badge_url(self, obj):
        if hasattr(obj, 'badge') and obj.badge.badge_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.badge.badge_image.url)
        return None


class ParticipantRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for participant registration"""
    visiting_interests = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="List of visiting interest IDs (max 2)"
    )

    class Meta:
        model = Participant
        fields = [
            'first_name', 'last_name', 'middle_name', 'email', 'phone',
            'institution_name', 'position', 'event', 'participant_type',
            'arrival_date', 'departure_date', 'profile_photo', 'remarks',
            'visiting_interests'
        ]

    def create(self, validated_data):
        visiting_interests_data = validated_data.pop('visiting_interests', [])
        participant = super().create(validated_data)

        # Create visiting interests relationships
        if visiting_interests_data:
            for index, interest_id in enumerate(visiting_interests_data[:2]):  # Limit to 2 interests
                try:
                    visiting_interest = VisitingInterest.objects.get(
                        id=interest_id,
                        event=participant.event,
                        is_active=True
                    )
                    # Check if not full
                    if not visiting_interest.is_full:
                        ParticipantVisitingInterest.objects.create(
                            participant=participant,
                            visiting_interest=visiting_interest,
                            priority=index + 1
                        )
                except VisitingInterest.DoesNotExist:
                    pass  # Skip invalid interest IDs

        # Create badge for the participant
        from badges.models import Badge
        badge = Badge.objects.create(participant=participant)

        # Generate QR code and badge
        try:
            badge.generate_badge()
        except Exception as e:
            # Log error but don't fail registration
            print(f"Error generating badge for {participant.full_name}: {e}")

        # Send registration confirmation email
        try:
            from events.email_service import get_email_service
            email_service = get_email_service()
            email_service.send_registration_confirmation(participant)
        except Exception as e:
            # Log error but don't fail registration
            print(f"Error sending confirmation email to {participant.email}: {e}")

        return participant


class ParticipantListSerializer(serializers.ModelSerializer):
    """Simplified serializer for participant list views"""
    full_name = serializers.CharField(read_only=True)
    participant_type_name = serializers.CharField(source='participant_type.name', read_only=True)
    participant_type_color = serializers.CharField(source='participant_type.color', read_only=True)
    event_name = serializers.CharField(source='event.name', read_only=True)

    # Hotel details
    assigned_hotel_name = serializers.CharField(source='assigned_hotel.name', read_only=True)
    assigned_hotel_address = serializers.CharField(source='assigned_hotel.address', read_only=True)
    assigned_hotel_phone = serializers.CharField(source='assigned_hotel.phone', read_only=True)
    assigned_hotel_email = serializers.CharField(source='assigned_hotel.email', read_only=True)

    # Driver details
    assigned_driver_name = serializers.CharField(source='assigned_driver.name', read_only=True)
    assigned_driver_phone = serializers.CharField(source='assigned_driver.phone', read_only=True)
    assigned_driver_car_model = serializers.CharField(source='assigned_driver.car_model', read_only=True)
    assigned_driver_car_plate = serializers.CharField(source='assigned_driver.car_plate', read_only=True)

    badge_generated = serializers.SerializerMethodField()
    badge_url = serializers.SerializerMethodField()

    class Meta:
        model = Participant
        fields = [
            'id', 'uuid', 'full_name', 'email', 'phone', 'institution_name',
            'position', 'participant_type_name', 'participant_type_color',
            'event_name', 'arrival_date', 'departure_date', 'is_confirmed',
            'registration_date', 'assigned_hotel', 'assigned_driver',
            'assigned_hotel_name', 'assigned_hotel_address', 'assigned_hotel_phone', 'assigned_hotel_email',
            'assigned_driver_name', 'assigned_driver_phone', 'assigned_driver_car_model', 'assigned_driver_car_plate',
            'badge_generated', 'badge_url', 'profile_photo', 'remarks'
        ]

    def get_badge_generated(self, obj):
        return hasattr(obj, 'badge') and bool(obj.badge.badge_image)

    def get_badge_url(self, obj):
        if hasattr(obj, 'badge') and obj.badge.badge_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.badge.badge_image.url)
        return None

# Health Check Script for Local Deployment

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "UoG Event Management - Health Check" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Check Docker containers
Write-Host "Checking Docker containers..." -ForegroundColor Yellow
try {
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Select-String "uogevent"
    if ($containers) {
        Write-Host "Running containers:" -ForegroundColor Green
        $containers | ForEach-Object { Write-Host $_ -ForegroundColor White }
    } else {
        Write-Host "No UoG Event containers found running" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Error checking Docker containers: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Check Backend API
Write-Host "Checking Backend API (http://localhost:8000)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Backend API is accessible (Status: $($response.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host "✗ Backend API returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Backend API is not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

# Check Admin Panel
Write-Host "Checking Admin Panel (http://localhost:8000/admin/)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/admin/login/" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Admin Panel is accessible (Status: $($response.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host "✗ Admin Panel returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Admin Panel is not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

# Check Frontend
Write-Host "Checking Frontend (http://localhost:3001)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Frontend is accessible (Status: $($response.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host "✗ Frontend returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Frontend is not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Health Check Complete" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "If services are accessible:" -ForegroundColor Yellow
Write-Host "- Frontend: http://localhost:3001" -ForegroundColor Blue
Write-Host "- Backend API: http://localhost:8000/api" -ForegroundColor Blue
Write-Host "- Admin Panel: http://localhost:8000/admin" -ForegroundColor Blue
Write-Host "- Default Admin: username=admin, password=admin123" -ForegroundColor Blue

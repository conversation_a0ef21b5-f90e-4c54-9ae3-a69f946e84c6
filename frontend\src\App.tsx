import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import './App.css';

// Context
import { AuthProvider } from './contexts/AuthContext';
import { ToastProvider } from './contexts/ToastContext';

// Components
import Navbar from './components/Navbar';
import ToastContainer from './components/ToastContainer';
import ProtectedRoute from './components/ProtectedRoute';
import Home from './pages/Home';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Events from './pages/Events';
import Register from './pages/Register';
import AdminDashboard from './pages/AdminDashboard';
import EventList from './pages/EventList';
import EventDetail from './pages/EventDetail';
import EventForm from './pages/EventForm';
import OrganizationList from './pages/OrganizationList';
import OrganizationForm from './pages/OrganizationForm';
import OrganizationDetail from './pages/OrganizationDetail';
import ParticipantRegistration from './pages/ParticipantRegistration';
import ParticipantList from './pages/ParticipantList';
import ParticipantManagement from './pages/ParticipantManagement';
import ParticipantEdit from './pages/ParticipantEdit';
import EventSchedule from './pages/EventSchedule';
import EventScheduleManagement from './pages/EventScheduleManagement';
import EventGallery from './pages/EventGallery';
import QRScanner from './pages/QRScanner';
import ParticipantVerify from './pages/ParticipantVerify';
import HotelList from './pages/HotelList';
import HotelDetail from './pages/HotelDetail';
import HotelForm from './pages/HotelForm';
import DriverList from './pages/DriverList';
import DriverDetail from './pages/DriverDetail';
import DriverForm from './pages/DriverForm';
import ParticipantTypeList from './pages/ParticipantTypeList';
import EmailManagement from './pages/EmailManagement';
import EventGalleryManagement from './pages/EventGalleryManagement';
import AttendanceManagement from './pages/AttendanceManagement';

function App() {
  return (
    <AuthProvider>
      <ToastProvider>
        <Router>
          <div className="App">
            <Navbar />
            <ToastContainer />
          <Routes>
            {/* Public Routes - Home without container */}
            <Route path="/" element={<Home />} />

            {/* Other public routes with container */}
            <Route path="/login" element={<div className="container-fluid"><Login /></div>} />
            <Route path="/events" element={<Events />} />
            <Route path="/register" element={<Register />} />
            <Route path="/participant-register" element={<ParticipantRegistration />} />
            <Route path="/verify/:uuid" element={<div className="container-fluid"><ParticipantVerify /></div>} />

              {/* Protected Routes */}
              <Route path="/dashboard" element={
                <div className="container-fluid">
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                </div>
              } />

              <Route path="/admin" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <AdminDashboard />
                  </ProtectedRoute>
                </div>
              } />

              <Route path="/events" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <EventList />
                  </ProtectedRoute>
                </div>
              } />

              <Route path="/events/new" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <EventForm />
                  </ProtectedRoute>
                </div>
              } />

              <Route path="/events/:id" element={<div className="container-fluid"><EventDetail /></div>} />
              <Route path="/events/:id/edit" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <EventForm />
                  </ProtectedRoute>
                </div>
              } />
              <Route path="/events/:id/schedule" element={<div className="container-fluid"><EventSchedule /></div>} />
              <Route path="/schedule-management" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <EventScheduleManagement />
                  </ProtectedRoute>
                </div>
              } />
              <Route path="/events/:id/gallery" element={<div className="container-fluid"><EventGallery /></div>} />

              <Route path="/participants" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <ParticipantList />
                  </ProtectedRoute>
                </div>
              } />

              <Route path="/participants/manage" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <ParticipantManagement />
                  </ProtectedRoute>
                </div>
              } />
              <Route path="/participants/:id/edit" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <ParticipantEdit />
                  </ProtectedRoute>
                </div>
              } />

              <Route path="/scan" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <QRScanner />
                  </ProtectedRoute>
                </div>
              } />

              {/* Organization Routes */}
              <Route path="/organizations" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <OrganizationList />
                  </ProtectedRoute>
                </div>
              } />
              <Route path="/organizations/new" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <OrganizationForm />
                  </ProtectedRoute>
                </div>
              } />
              <Route path="/organizations/:id" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <OrganizationDetail />
                  </ProtectedRoute>
                </div>
              } />
              <Route path="/organizations/:id/edit" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <OrganizationForm />
                  </ProtectedRoute>
                </div>
              } />

              {/* Hotel Management Routes */}
              <Route path="/hotels" element={<div className="container-fluid"><HotelList /></div>} />
              <Route path="/hotels/new" element={<div className="container-fluid"><HotelForm /></div>} />
              <Route path="/hotels/:id" element={<div className="container-fluid"><HotelDetail /></div>} />
              <Route path="/hotels/:id/edit" element={<div className="container-fluid"><HotelForm /></div>} />

              {/* Driver Management Routes */}
              <Route path="/drivers" element={<div className="container-fluid"><DriverList /></div>} />
              <Route path="/drivers/new" element={<div className="container-fluid"><DriverForm /></div>} />
              <Route path="/drivers/:id" element={<div className="container-fluid"><DriverDetail /></div>} />
              <Route path="/drivers/:id/edit" element={<div className="container-fluid"><DriverForm /></div>} />
              <Route path="/drivers/:id/assignments" element={<div className="container-fluid"><DriverDetail /></div>} />

              {/* Participant Type Management Routes */}
              <Route path="/participant-types" element={<div className="container-fluid"><ParticipantTypeList /></div>} />

              {/* Email Management Routes */}
              <Route path="/email-management" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <EmailManagement />
                  </ProtectedRoute>
                </div>
              } />

              {/* Gallery Management Routes */}
              <Route path="/gallery-management" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <EventGalleryManagement />
                  </ProtectedRoute>
                </div>
              } />

              {/* Attendance Management Routes */}
              <Route path="/attendance-management" element={
                <div className="container-fluid">
                  <ProtectedRoute requireAdmin={true}>
                    <AttendanceManagement />
                  </ProtectedRoute>
                </div>
              } />
            </Routes>
          </div>
        </Router>
      </ToastProvider>
    </AuthProvider>
  );
}

export default App;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Branding API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .organization-info {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .logo {
            max-width: 200px;
            height: auto;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>University of Gondar Branding Test</h1>
    
    <button onclick="testBrandingAPI()">Test Branding API</button>
    
    <div id="result"></div>

    <script>
        async function testBrandingAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Loading...</p>';
            
            try {
                const response = await fetch('http://localhost:8000/api/organizations/primary/');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const organization = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ API Response Successful</h3>
                    </div>
                    <div class="organization-info">
                        <h2>${organization.name}</h2>
                        ${organization.logo ? `<img src="http://localhost:8000${organization.logo}" alt="${organization.name}" class="logo">` : '<p>No logo available</p>'}
                        <p><strong>Short Name:</strong> ${organization.short_name || 'N/A'}</p>
                        <p><strong>Motto:</strong> ${organization.motto || 'N/A'}</p>
                        <p><strong>Email:</strong> ${organization.email || 'N/A'}</p>
                        <p><strong>Phone:</strong> ${organization.phone || 'N/A'}</p>
                        <p><strong>Website:</strong> ${organization.website || 'N/A'}</p>
                        <p><strong>Address:</strong> ${organization.full_address || 'N/A'}</p>
                        <p><strong>Primary Organization:</strong> ${organization.is_primary ? 'Yes' : 'No'}</p>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Error Testing API</h3>
                        <p>${error.message}</p>
                        <p>Make sure the Django backend is running on http://localhost:8000</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>

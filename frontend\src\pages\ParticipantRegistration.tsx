import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON>, But<PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { eventService, participantService, participantTypeService, visitingInterestService, Event, ParticipantType, VisitingInterest } from '../services/api';
import LandingNavbar from '../components/LandingNavbar';
import Footer from '../components/Footer';
import ValidatedFormField from '../components/ValidatedFormField';
import { useToast } from '../contexts/ToastContext';
import { validateParticipantForm, FormErrors } from '../utils/validation';
import '../styles/landing.css';

// Custom styles for DatePicker
const datePickerStyles = `
  .react-datepicker-wrapper {
    width: 100%;
  }
  .react-datepicker__input-container input {
    border-radius: 12px !important;
    font-size: 16px !important;
    padding: 12px 16px !important;
  }
`;

const ParticipantRegistration: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [events, setEvents] = useState<Event[]>([]);
  const [participantTypes, setParticipantTypes] = useState<ParticipantType[]>([]);
  const [visitingInterests, setVisitingInterests] = useState<VisitingInterest[]>([]);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [validationErrors, setValidationErrors] = useState<FormErrors>({});
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    middle_name: '',
    email: '',
    phone: '',
    institution_name: '',
    position: '',
    event: '',
    participant_type: '',
    arrival_date: new Date(),
    departure_date: new Date(),
    profile_photo: null as File | null,
    remarks: '',
    visiting_interests: [] as number[],
  });

  const [consentAgreed, setConsentAgreed] = useState(false);

  // Validation functions
  const validateField = async (fieldName: string, value: any) => {
    const tempFormData = { ...formData, [fieldName]: value };
    const errors = validateParticipantForm(tempFormData);

    // For email field, also check uniqueness (only during field blur, not on submit)
    if (fieldName === 'email' && value && !errors[fieldName]) {
      try {
        const { validateEmailUniqueness } = await import('../utils/validation');
        const uniquenessResult = await validateEmailUniqueness(value);
        if (!uniquenessResult.isValid) {
          errors[fieldName] = uniquenessResult.message!;
        }
      } catch (error) {
        console.warn('Email uniqueness check failed:', error);
      }
    }

    setValidationErrors(prev => ({
      ...prev,
      [fieldName]: errors[fieldName] || ''
    }));
  };

  // Fast validation for form submission (no async checks)
  const validateFormSync = () => {
    const errors = validateParticipantForm(formData);
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleFieldChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({ ...prev, [name]: '' }));
    }

    // For critical fields, validate immediately
    if (['email', 'departure_date'].includes(name) && value.trim()) {
      setTimeout(() => validateField(name, value), 500); // Debounce validation
    }
  };

  const handleFieldBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setTouchedFields(prev => new Set(prev).add(name));
    validateField(name, value);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({ ...prev, profile_photo: file }));

    if (file) {
      validateField('profile_photo', file);
    }
  };

  const handleVisitingInterestChange = (interestId: number, checked: boolean) => {
    setFormData(prev => {
      let newInterests = [...prev.visiting_interests];

      if (checked) {
        // Add interest if not already selected and limit to 2
        if (!newInterests.includes(interestId)) {
          if (newInterests.length < 2) {
            newInterests.push(interestId);
          } else {
            // Show toast when trying to select more than 2
            showToast({
              type: 'warning',
              title: 'Selection Limit',
              message: 'You can only select up to 2 places to visit.'
            });
            return prev; // Don't change the state
          }
        }
      } else {
        // Remove interest
        newInterests = newInterests.filter(id => id !== interestId);
      }

      return { ...prev, visiting_interests: newInterests };
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [eventsResponse, typesResponse] = await Promise.all([
          eventService.getEvents(),
          participantTypeService.getParticipantTypes(),
        ]);

        // Handle paginated responses
        const eventsData = Array.isArray(eventsResponse.data)
          ? eventsResponse.data
          : (eventsResponse.data as any).results || [];
        const typesData = Array.isArray(typesResponse.data)
          ? typesResponse.data
          : (typesResponse.data as any).results || [];



        setEvents(eventsData.filter((event: any) => event.is_active));
        setParticipantTypes(typesData);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load registration data');
      }
    };

    fetchData();
  }, []);

  // Load visiting interests when event is selected
  useEffect(() => {
    const fetchVisitingInterests = async () => {
      if (formData.event) {
        try {
          const response = await visitingInterestService.getVisitingInterests(parseInt(formData.event));
          const interestsData = Array.isArray(response.data)
            ? response.data
            : (response.data as any).results || [];
          setVisitingInterests(interestsData);
        } catch (error) {
          console.error('Error fetching visiting interests:', error);
        }
      } else {
        setVisitingInterests([]);
        setFormData(prev => ({ ...prev, visiting_interests: [] }));
      }
    };

    fetchVisitingInterests();
  }, [formData.event]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({ ...prev, [name]: '' }));
    }
  };



  const handleDateChange = (date: Date | null, field: 'arrival_date' | 'departure_date') => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        [field]: date,
      }));

      // Clear any existing error for this field
      if (validationErrors[field]) {
        setValidationErrors(prev => ({ ...prev, [field]: '' }));
      }

      // Validate date range if both dates are set
      setTimeout(() => {
        const updatedFormData = { ...formData, [field]: date };
        if (updatedFormData.arrival_date && updatedFormData.departure_date) {
          const errors = validateParticipantForm(updatedFormData);
          if (field === 'departure_date' && errors.departure_date) {
            setValidationErrors(prev => ({ ...prev, departure_date: errors.departure_date }));
          }
        }
      }, 100);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      // Validate form (fast sync validation)
      if (!validateFormSync()) {
        setLoading(false);

        // Get field names with errors for better user feedback
        const errorFields = Object.keys(validationErrors).filter(key => validationErrors[key]);
        const fieldLabels: { [key: string]: string } = {
          first_name: 'First Name',
          last_name: 'Last Name',
          middle_name: 'Middle Name',
          email: 'Email',
          phone: 'Phone',
          institution_name: 'Institution',
          position: 'Position',
          event: 'Event',
          participant_type: 'Participant Type',
          arrival_date: 'Arrival Date',
          departure_date: 'Departure Date',
          profile_photo: 'Profile Photo'
        };

        const errorFieldNames = errorFields.map(field => fieldLabels[field] || field).join(', ');

        showToast({
          type: 'error',
          title: 'Validation Error',
          message: `Please fix the errors in: ${errorFieldNames}`
        });
        return;
      }

      // Validate consent agreement
      if (!consentAgreed) {
        setLoading(false);
        showToast({
          type: 'error',
          title: 'Consent Required',
          message: 'Please agree to the photo/video consent before submitting.'
        });
        return;
      }

      // Create FormData for file upload
      const submitData = new FormData();
      submitData.append('first_name', formData.first_name);
      submitData.append('last_name', formData.last_name);
      submitData.append('middle_name', formData.middle_name);
      submitData.append('email', formData.email);
      submitData.append('phone', formData.phone);
      submitData.append('institution_name', formData.institution_name);
      submitData.append('position', formData.position);
      submitData.append('event', formData.event);
      submitData.append('participant_type', formData.participant_type);
      submitData.append('arrival_date', formData.arrival_date.toISOString());
      submitData.append('departure_date', formData.departure_date.toISOString());
      if (formData.profile_photo) {
        submitData.append('profile_photo', formData.profile_photo);
      }
      submitData.append('remarks', formData.remarks);

      // Add visiting interests
      if (formData.visiting_interests.length > 0) {
        formData.visiting_interests.forEach((interestId) => {
          submitData.append('visiting_interests', interestId.toString());
        });
      }

      await participantService.registerParticipant(submitData);

      // Show success toast
      showToast({
        type: 'success',
        title: 'Registration Successful!',
        message: 'Your registration has been submitted successfully. You will receive a confirmation email shortly.',
        duration: 8000
      });

      // Reset form
      setFormData({
        first_name: '',
        last_name: '',
        middle_name: '',
        email: '',
        phone: '',
        institution_name: '',
        position: '',
        event: '',
        participant_type: '',
        arrival_date: new Date(),
        departure_date: new Date(),
        profile_photo: null,
        remarks: '',
        visiting_interests: [],
      });
      setConsentAgreed(false);

      // Redirect to home page after a short delay
      setTimeout(() => {
        navigate('/');
      }, 3000); // 3 second delay to allow user to see the success message
      setValidationErrors({});
      setTouchedFields(new Set());

      // Reset file input
      const fileInput = document.getElementById('profile_photo') as HTMLInputElement;
      if (fileInput) fileInput.value = '';

      // Scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });

    } catch (error: any) {
      console.error('Registration error:', error);

      let errorMessage = 'Registration failed. Please try again.';

      // Handle specific error cases
      if (error.response?.status === 400) {
        const errorData = error.response.data;

        // Handle email uniqueness error
        if (errorData.email && errorData.email.includes('already exists')) {
          errorMessage = 'This email address is already registered. Please use a different email address or contact support if this is your email.';
        } else if (errorData.email) {
          errorMessage = `Email error: ${errorData.email[0]}`;
        } else if (errorData.non_field_errors) {
          errorMessage = errorData.non_field_errors[0];
        } else {
          errorMessage = errorData.detail || 'Please check your input and try again.';
        }
      } else {
        errorMessage = error.response?.data?.detail || error.message || 'Registration failed. Please try again.';
      }

      setError(errorMessage);

      showToast({
        type: 'error',
        title: 'Registration Failed',
        message: errorMessage
      });

      // Scroll to top to show error message
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="participant-registration-page">
      <style>{datePickerStyles}</style>

      {/* Navigation Menu */}
      <LandingNavbar useHomeNavigation={false} />

      <div className="min-vh-100 position-relative overflow-hidden" style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        paddingTop: '120px',
        paddingBottom: '2rem'
      }}>
        {/* Particles Background */}
        <div className="particles position-absolute w-100 h-100">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="particle position-absolute rounded-circle bg-white"
              style={{
                width: Math.random() * 4 + 2 + 'px',
                height: Math.random() * 4 + 2 + 'px',
                left: Math.random() * 100 + '%',
                top: Math.random() * 100 + '%',
                opacity: Math.random() * 0.3 + 0.1,
                animation: `float ${Math.random() * 3 + 2}s ease-in-out infinite alternate`
              }}
            />
          ))}
        </div>

        <div className="section-container position-relative">
        {/* Header Section */}
        <Row className="justify-content-center mb-5">
          <Col lg={10} xl={8}>
            <div className="text-center text-white mb-5">
              <div className="mb-4">
                <i className="fas fa-user-plus fa-4x mb-3 text-warning" style={{
                  opacity: 0.9,
                  textShadow: '0 4px 8px rgba(0,0,0,0.3)'
                }}></i>
              </div>
              <h1 className="display-3 fw-bold mb-4 hero-title" style={{
                textShadow: '0 4px 8px rgba(0,0,0,0.3)'
              }}>
                Event Registration
              </h1>
              <p className="lead fs-4 mb-4 hero-description" style={{
                opacity: 0.95,
                textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                maxWidth: '600px',
                margin: '0 auto'
              }}>
                Join us for an amazing experience at the University of Gondar. Register now to secure your spot at our upcoming events!
              </p>


            </div>
          </Col>
        </Row>

        <Row className="justify-content-center">
          <Col lg={11} xl={10}>
            <div className="hero-glass-panel rounded-4 overflow-hidden" style={{
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 25px 50px rgba(0, 0, 0, 0.15)'
            }}>
              {/* Card Header with Gradient */}
              <div
                className="text-white p-4"
                style={{
                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                  borderRadius: '20px 20px 0 0'
                }}
              >
                <Row className="align-items-center">
                  <Col>
                    <h3 className="mb-0 fw-bold" style={{
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                    }}>
                      <i className="fas fa-edit me-3"></i>
                      Registration Form
                    </h3>
                    <p className="mb-0 mt-2" style={{
                      opacity: 0.95,
                      textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                    }}>
                      Please fill in all required information accurately
                    </p>
                  </Col>
                  <Col xs="auto">
                    <div className="text-end">
                      <i className="fas fa-shield-alt fa-2x" style={{
                        opacity: 0.8,
                        textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                      }}></i>
                    </div>
                  </Col>
                </Row>
              </div>

              <div className="p-5" style={{
                background: 'rgba(248, 251, 252, 0.8)',
                backdropFilter: 'blur(10px)'
              }}>


                {error && (
                  <Alert
                    variant="danger"
                    className="mb-4 border-0 shadow-sm"
                    style={{
                      borderRadius: '15px',
                      background: 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)'
                    }}
                  >
                    <div className="d-flex align-items-center">
                      <div className="me-3">
                        <i className="fas fa-exclamation-triangle fa-2x text-danger"></i>
                      </div>
                      <div>
                        <h5 className="mb-1 text-danger fw-bold">Registration Failed</h5>
                        <p className="mb-0">{error}</p>
                      </div>
                    </div>
                  </Alert>
                )}

                <Form onSubmit={handleSubmit}>
                  {/* Personal Information Section */}
                  <div className="mb-5">
                    <div className="d-flex align-items-center mb-4">
                      <div
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{
                          width: '50px',
                          height: '50px',
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          color: 'white'
                        }}
                      >
                        <i className="fas fa-user fa-lg"></i>
                      </div>
                      <div>
                        <h4 className="mb-0 fw-bold text-dark">Personal Information</h4>
                        <p className="mb-0 text-muted">Tell us about yourself</p>
                      </div>
                    </div>

                    <Row>
                      <Col md={6}>
                        <ValidatedFormField
                          label={<><i className="fas fa-user me-2 text-primary"></i>First Name</>}
                          name="first_name"
                          value={formData.first_name}
                          onChange={handleFieldChange}
                          onBlur={handleFieldBlur}
                          error={touchedFields.has('first_name') ? validationErrors.first_name : ''}
                          placeholder="Enter your first name"
                          required
                          size="lg"
                          className="mb-4"
                        />
                      </Col>
                      <Col md={6}>
                        <ValidatedFormField
                          label={<><i className="fas fa-user me-2 text-primary"></i>Last Name</>}
                          name="last_name"
                          value={formData.last_name}
                          onChange={handleFieldChange}
                          onBlur={handleFieldBlur}
                          error={touchedFields.has('last_name') ? validationErrors.last_name : ''}
                          placeholder="Enter your last name"
                          required
                          size="lg"
                          className="mb-4"
                        />
                      </Col>
                    </Row>

                    <Row>
                      <Col md={6}>
                        <ValidatedFormField
                          label={<><i className="fas fa-user me-2 text-secondary"></i>Middle Name</>}
                          name="middle_name"
                          value={formData.middle_name}
                          onChange={handleFieldChange}
                          onBlur={handleFieldBlur}
                          error={touchedFields.has('middle_name') ? validationErrors.middle_name : ''}
                          placeholder="Enter your middle name (optional)"
                          size="lg"
                          className="mb-4"
                          helpText="Optional field"
                        />
                      </Col>
                      <Col md={6}>
                        <ValidatedFormField
                          label={<><i className="fas fa-envelope me-2 text-primary"></i>Email Address</>}
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleFieldChange}
                          onBlur={handleFieldBlur}
                          error={touchedFields.has('email') ? validationErrors.email : ''}
                          placeholder="Enter your email address"
                          required
                          size="lg"
                          className="mb-4"
                        />
                      </Col>
                    </Row>
                  </div>

                  {/* Contact & Professional Information Section */}
                  <div className="mb-5">
                    <div className="d-flex align-items-center mb-4">
                      <div
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{
                          width: '50px',
                          height: '50px',
                          background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                          color: 'white'
                        }}
                      >
                        <i className="fas fa-briefcase fa-lg"></i>
                      </div>
                      <div>
                        <h4 className="mb-0 fw-bold text-dark">Professional Information</h4>
                        <p className="mb-0 text-muted">Your contact and professional details</p>
                      </div>
                    </div>

                    <Row>
                      <Col md={6}>
                        <ValidatedFormField
                          label={<><i className="fas fa-phone me-2 text-primary"></i>Phone Number</>}
                          name="phone"
                          type="tel"
                          value={formData.phone}
                          onChange={handleFieldChange}
                          onBlur={handleFieldBlur}
                          error={touchedFields.has('phone') ? validationErrors.phone : ''}
                          placeholder="Enter your phone number"
                          required
                          size="lg"
                          className="mb-4"
                          helpText="Include country code if international"
                        />
                      </Col>
                      <Col md={6}>
                        <ValidatedFormField
                          label={<><i className="fas fa-user-tie me-2 text-primary"></i>Position</>}
                          name="position"
                          value={formData.position}
                          onChange={handleFieldChange}
                          onBlur={handleFieldBlur}
                          error={touchedFields.has('position') ? validationErrors.position : ''}
                          placeholder="Enter your position/title"
                          required
                          size="lg"
                          className="mb-4"
                        />
                      </Col>
                    </Row>

                    <ValidatedFormField
                      label={<><i className="fas fa-building me-2 text-primary"></i>Institution Name</>}
                      name="institution_name"
                      value={formData.institution_name}
                      onChange={handleFieldChange}
                      onBlur={handleFieldBlur}
                      error={touchedFields.has('institution_name') ? validationErrors.institution_name : ''}
                      placeholder="Enter your institution/organization name"
                      required
                      size="lg"
                      className="mb-4"
                    />
                  </div>

                  {/* Event Information Section */}
                  <div className="mb-5">
                    <div className="d-flex align-items-center mb-4">
                      <div
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{
                          width: '50px',
                          height: '50px',
                          background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
                          color: 'white'
                        }}
                      >
                        <i className="fas fa-calendar-alt fa-lg"></i>
                      </div>
                      <div>
                        <h4 className="mb-0 fw-bold text-dark">Event Information</h4>
                        <p className="mb-0 text-muted">Select your event and participation type</p>
                      </div>
                    </div>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-calendar-check me-2 text-primary"></i>
                            Event *
                          </Form.Label>
                          <Form.Select
                            name="event"
                            value={formData.event}
                            onChange={handleInputChange}
                            required
                            className="form-control-lg border-0 shadow-sm"
                            style={{
                              borderRadius: '12px',
                              backgroundColor: 'white',
                              fontSize: '16px'
                            }}
                          >
                            <option value="">
                              {events.length === 0 ? 'Loading events...' : 'Select an event'}
                            </option>
                            {events.map(event => (
                              <option key={event.id} value={event.id}>
                                {event.name} - {new Date(event.start_date).toLocaleDateString()}
                              </option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-user-tag me-2 text-primary"></i>
                            Participant Type *
                          </Form.Label>
                          <Form.Select
                            name="participant_type"
                            value={formData.participant_type}
                            onChange={handleInputChange}
                            required
                            className="form-control-lg border-0 shadow-sm"
                            style={{
                              borderRadius: '12px',
                              backgroundColor: 'white',
                              fontSize: '16px'
                            }}
                          >
                            <option value="">
                              {participantTypes.length === 0 ? 'Loading participant types...' : 'Select participant type'}
                            </option>
                            {participantTypes.map(type => (
                              <option key={type.id} value={type.id}>
                                {type.name}
                              </option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      </Col>
                    </Row>
                  </div>

                  {/* Travel Information Section */}
                  <div className="mb-5">
                    <div className="d-flex align-items-center mb-4">
                      <div
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{
                          width: '50px',
                          height: '50px',
                          background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                          color: 'white'
                        }}
                      >
                        <i className="fas fa-plane fa-lg"></i>
                      </div>
                      <div>
                        <h4 className="mb-0 fw-bold text-dark">Travel Information</h4>
                        <p className="mb-0 text-muted">Your arrival and departure details</p>
                      </div>
                    </div>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-plane-arrival me-2 text-primary"></i>
                            Arrival Date/Time *
                          </Form.Label>
                          <div>
                            <DatePicker
                              selected={formData.arrival_date}
                              onChange={(date) => handleDateChange(date, 'arrival_date')}
                              showTimeSelect
                              dateFormat="MMMM d, yyyy h:mm aa"
                              className={`form-control form-control-lg border-0 shadow-sm ${validationErrors.arrival_date ? 'is-invalid' : ''}`}
                              required
                              placeholderText="Select arrival date and time"
                            />
                            {validationErrors.arrival_date && (
                              <div className="invalid-feedback d-block">
                                <i className="fas fa-exclamation-circle me-1"></i>
                                {validationErrors.arrival_date}
                              </div>
                            )}
                          </div>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold text-dark">
                            <i className="fas fa-plane-departure me-2 text-primary"></i>
                            Departure Date/Time *
                          </Form.Label>
                          <div>
                            <DatePicker
                              selected={formData.departure_date}
                              onChange={(date) => handleDateChange(date, 'departure_date')}
                              showTimeSelect
                              dateFormat="MMMM d, yyyy h:mm aa"
                              className={`form-control form-control-lg border-0 shadow-sm ${validationErrors.departure_date ? 'is-invalid' : ''}`}
                              required
                              placeholderText="Select departure date and time"
                            />
                            {validationErrors.departure_date && (
                              <div className="invalid-feedback d-block">
                                <i className="fas fa-exclamation-circle me-1"></i>
                                {validationErrors.departure_date}
                              </div>
                            )}
                          </div>
                        </Form.Group>
                      </Col>
                    </Row>
                  </div>

                  {/* Additional Information Section */}
                  <div className="mb-5">
                    <div className="d-flex align-items-center mb-4">
                      <div
                        className="rounded-circle d-flex align-items-center justify-content-center me-3"
                        style={{
                          width: '50px',
                          height: '50px',
                          background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                          color: 'white'
                        }}
                      >
                        <i className="fas fa-camera fa-lg"></i>
                      </div>
                      <div>
                        <h4 className="mb-0 fw-bold text-dark">Additional Information</h4>
                        <p className="mb-0 text-muted">Upload your photo and add any remarks</p>
                      </div>
                    </div>

                    <Form.Group className="mb-4">
                      <Form.Label className="fw-semibold text-dark">
                        <i className="fas fa-image me-2 text-primary"></i>
                        Profile Photo for Badge (Optional)
                      </Form.Label>
                      <Form.Control
                        type="file"
                        id="profile_photo"
                        accept="image/*"
                        onChange={handleFileChange}
                        className="form-control-lg border-0 shadow-sm"
                        style={{
                          borderRadius: '12px',
                          backgroundColor: 'white',
                          fontSize: '16px'
                        }}
                      />
                      <Form.Text className="text-muted mt-2">
                        <i className="fas fa-info-circle me-1"></i>
                        Upload a clear photo for your event badge. If not provided, a default badge will be generated.
                      </Form.Text>
                    </Form.Group>

                    <Form.Group className="mb-4">
                      <Form.Label className="fw-semibold text-dark">
                        <i className="fas fa-comment me-2 text-secondary"></i>
                        Remarks
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={4}
                        name="remarks"
                        value={formData.remarks}
                        onChange={handleInputChange}
                        className="form-control-lg border-0 shadow-sm"
                        style={{
                          borderRadius: '12px',
                          backgroundColor: 'white',
                          fontSize: '16px',
                          resize: 'vertical'
                        }}
                        placeholder="Any additional information, special requirements, or dietary restrictions..."
                      />
                    </Form.Group>
                  </div>

                  {/* Visiting Interests */}
                  {visitingInterests.length > 0 && (
                    <div className="mb-4">
                      <Form.Label className="fw-semibold text-dark mb-3">
                        <i className="fas fa-map-marker-alt me-2 text-primary"></i>
                        Places to Visit <span className="text-muted small">(Select up to 2)</span>
                        <span className="ms-2 badge bg-primary">{formData.visiting_interests.length}/2</span>
                      </Form.Label>

                      <div className="row g-2">
                        {visitingInterests.map((interest) => (
                          <div key={interest.id} className="col-md-6">
                            <div
                              className="card border-0 shadow-sm position-relative"
                              style={{
                                transition: 'all 0.2s ease',
                                cursor: formData.visiting_interests.includes(interest.id) || formData.visiting_interests.length < 2 ? 'pointer' : 'not-allowed',
                                opacity: (!formData.visiting_interests.includes(interest.id) && formData.visiting_interests.length >= 2) ? 0.5 : 1,
                                transform: 'scale(1)',
                                height: '60px'
                              }}
                              onMouseEnter={(e) => {
                                if (formData.visiting_interests.includes(interest.id) || formData.visiting_interests.length < 2) {
                                  e.currentTarget.style.transform = 'scale(1.02)';
                                  e.currentTarget.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
                                }
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.transform = 'scale(1)';
                                e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                              }}
                              title={`${interest.description}\n📍 ${interest.location || 'Location TBA'}\n👥 ${interest.current_participants_count || 0}/${interest.max_participants || '∞'} participants`}
                            >
                              <div className="card-body p-3 d-flex align-items-center">
                                <Form.Check
                                  type="checkbox"
                                  id={`interest-${interest.id}`}
                                  checked={formData.visiting_interests.includes(interest.id)}
                                  onChange={(e) => handleVisitingInterestChange(interest.id, e.target.checked)}
                                  disabled={
                                    !formData.visiting_interests.includes(interest.id) &&
                                    formData.visiting_interests.length >= 2
                                  }
                                  className="me-3"
                                />
                                <div className="flex-grow-1">
                                  <div className="fw-semibold text-dark">{interest.name}</div>
                                  <div className="text-muted small d-flex align-items-center">
                                    <i className="fas fa-location-dot me-1"></i>
                                    {interest.location || 'Location TBA'}
                                    {interest.max_participants && (
                                      <>
                                        <span className="mx-2">•</span>
                                        <i className="fas fa-users me-1"></i>
                                        {interest.current_participants_count || 0}/{interest.max_participants}
                                      </>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {formData.visiting_interests.length === 0 && (
                        <div className="text-muted small mt-2">
                          <i className="fas fa-info-circle me-1"></i>
                          Hover over options to see details. Select up to 2 places you'd like to visit.
                        </div>
                      )}
                    </div>
                  )}

                  {/* Photo/Video Consent */}
                  <div className="mb-4">
                    <div className="card border-0 shadow-sm" style={{ backgroundColor: '#f8f9fa' }}>
                      <div className="card-body">
                        <h6 className="fw-bold text-dark mb-3">
                          <i className="fas fa-camera me-2 text-primary"></i>
                          Photo/Video Consent
                        </h6>
                        <Form.Check
                          type="checkbox"
                          id="consent-checkbox"
                          checked={consentAgreed}
                          onChange={(e) => setConsentAgreed(e.target.checked)}
                          label={
                            <span className="text-dark">
                              I agree that photos and videos may be taken during the event for promotional and documentation purposes.
                              I understand that these materials may be used by the University of Gondar for marketing, social media,
                              and other institutional purposes.
                            </span>
                          }
                          className="mb-0"
                          style={{ fontSize: '14px' }}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="text-center">
                    <Button
                      type="submit"
                      size="lg"
                      disabled={loading || !consentAgreed}
                      className="px-5 py-3 border-0 fw-bold"
                      style={{
                        background: loading || !consentAgreed
                          ? 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
                          : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        borderRadius: '50px',
                        fontSize: '18px',
                        boxShadow: consentAgreed && !loading ? '0 8px 25px rgba(102, 126, 234, 0.3)' : '0 4px 15px rgba(108, 117, 125, 0.2)',
                        transition: 'all 0.3s ease',
                        minWidth: '250px',
                        opacity: consentAgreed || loading ? 1 : 0.7,
                      }}
                    >
                      {loading ? (
                        <>
                          <Spinner animation="border" size="sm" className="me-3" />
                          Processing Registration...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-rocket me-3"></i>
                          Complete Registration
                        </>
                      )}
                    </Button>

                    {!consentAgreed && !loading && (
                      <p className="text-warning mt-3 mb-2">
                        <i className="fas fa-exclamation-triangle me-2"></i>
                        Please agree to the photo/video consent to complete registration
                      </p>
                    )}

                    <p className="text-muted mt-3 mb-4">
                      <i className="fas fa-shield-alt me-2"></i>
                      Your information is secure and will only be used for event purposes
                    </p>

                    {/* Back to Home Button */}
                    <Link
                      to="/"
                      className="btn btn-outline-primary btn-lg px-4 mt-3"
                      style={{
                        borderRadius: '50px',
                        fontSize: '16px',
                        borderWidth: '2px'
                      }}
                    >
                      <i className="fas fa-arrow-left me-2"></i>
                      Back to Home
                    </Link>
                  </div>
                </Form>
              </div>
            </div>
          </Col>
        </Row>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default ParticipantRegistration;

from rest_framework import serializers
from .models import Event, EventSchedule, EventGallery, EmailConfiguration, EmailTemplate, EmailLog


class EventScheduleSerializer(serializers.ModelSerializer):
    class Meta:
        model = EventSchedule
        fields = '__all__'


class EventGallerySerializer(serializers.ModelSerializer):
    class Meta:
        model = EventGallery
        fields = '__all__'


class EventSerializer(serializers.ModelSerializer):
    schedules = EventScheduleSerializer(many=True, read_only=True)
    gallery = EventGallerySerializer(many=True, read_only=True)
    participant_count = serializers.SerializerMethodField()

    class Meta:
        model = Event
        fields = '__all__'

    def get_participant_count(self, obj):
        return obj.participants.count()


class EventListSerializer(serializers.ModelSerializer):
    """Simplified serializer for event list views"""
    participant_count = serializers.SerializerMethodField()

    class Meta:
        model = Event
        fields = ['id', 'name', 'description', 'start_date', 'end_date', 
                 'location', 'city', 'country', 'logo', 'banner', 
                 'is_active', 'participant_count']

    def get_participant_count(self, obj):
        return obj.participants.count()


class EmailConfigurationSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmailConfiguration
        fields = '__all__'
        extra_kwargs = {
            'email_host_password': {'write_only': True}
        }


class EmailTemplateSerializer(serializers.ModelSerializer):
    template_type_display = serializers.CharField(source='get_template_type_display', read_only=True)

    class Meta:
        model = EmailTemplate
        fields = '__all__'


class EmailLogSerializer(serializers.ModelSerializer):
    template_type_display = serializers.CharField(source='get_template_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = EmailLog
        fields = '__all__'

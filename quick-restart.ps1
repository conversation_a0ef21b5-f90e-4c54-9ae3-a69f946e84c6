# Quick Restart Script for Development
# Use this when you make changes and need to rebuild/restart

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Quick Restart - UoG Event System" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$projectPath = "C:\Users\<USER>\Desktop\uog event"
Set-Location $projectPath

try {
    Write-Host "[1/4] Stopping containers..." -ForegroundColor Yellow
    docker-compose down
    Write-Host ""

    Write-Host "[2/4] Rebuilding backend (with changes)..." -ForegroundColor Yellow
    docker-compose build backend
    Write-Host "✓ Backend rebuilt" -ForegroundColor Green
    Write-Host ""

    Write-Host "[3/4] Starting all services..." -ForegroundColor Yellow
    docker-compose up -d
    Write-Host "Waiting for services to start..." -ForegroundColor Gray
    Start-Sleep -Seconds 15
    Write-Host "✓ Services started" -ForegroundColor Green
    Write-Host ""

    Write-Host "[4/4] Testing API..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/api/events/" -UseBasicParsing -TimeoutSec 10
        Write-Host "✓ API responding (HTTP $($response.StatusCode))" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠ API test failed, but services may still be starting..." -ForegroundColor Yellow
    }
    Write-Host ""

    Write-Host "🎉 Quick restart complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Services:" -ForegroundColor White
    Write-Host "- Backend: http://localhost:8000" -ForegroundColor Blue
    Write-Host "- Frontend: http://localhost:3001 (if running)" -ForegroundColor Blue
    Write-Host ""

    docker-compose ps
}
catch {
    Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

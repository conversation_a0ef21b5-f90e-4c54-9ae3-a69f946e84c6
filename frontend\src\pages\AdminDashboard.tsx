import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Modal, <PERSON>, Badge, <PERSON><PERSON>, Spin<PERSON>, Nav, Tab, ProgressBar } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { authService, User, UserRole } from '../services/auth';
import { eventService, participantService } from '../services/api';

const AdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [systemStats, setSystemStats] = useState({
    totalEvents: 0,
    totalParticipants: 0,
    totalUsers: 0,
    activeEvents: 0,
    confirmedParticipants: 0,
    pendingParticipants: 0
  });
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    phone: '',
    position: '',
    institution: '',
    role: '',
    password: '',
    password_confirm: ''
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [usersResponse, rolesResponse, eventsResponse, participantsResponse] = await Promise.all([
        authService.getUsers(),
        authService.getUserRoles(),
        eventService.getEvents().catch(() => ({ data: [] })),
        participantService.getParticipants().catch(() => ({ data: [] }))
      ]);

      // Ensure users is always an array
      const users = Array.isArray(usersResponse) ? usersResponse : (usersResponse as any)?.results || [];
      const roles = Array.isArray(rolesResponse) ? rolesResponse : (rolesResponse as any)?.results || [];

      setUsers(users);
      setRoles(roles);

      // Calculate system statistics
      const events = Array.isArray(eventsResponse.data) ? eventsResponse.data : (eventsResponse.data as any)?.results || [];
      const participants = Array.isArray(participantsResponse.data) ? participantsResponse.data : (participantsResponse.data as any)?.results || [];

      setSystemStats({
        totalEvents: events.length,
        totalParticipants: participants.length,
        totalUsers: users.length,
        activeEvents: events.filter((e: any) => e.is_active).length,
        confirmedParticipants: participants.filter((p: any) => p.is_confirmed).length,
        pendingParticipants: participants.filter((p: any) => !p.is_confirmed).length
      });

    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load admin data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const createData = {
        ...formData,
        role: parseInt(formData.role) // Convert string to number
      };
      await authService.createUser(createData);
      setShowCreateModal(false);
      resetForm();
      fetchData();
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Failed to create user');
    }
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingUser) return;

    try {
      const updateData = {
        ...formData,
        role: parseInt(formData.role) // Convert string to number
      };
      await authService.updateUser(editingUser.id, updateData);
      setEditingUser(null);
      resetForm();
      fetchData();
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Failed to update user');
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (!window.confirm('Are you sure you want to delete this user?')) return;

    try {
      await authService.deleteUser(userId);
      fetchData();
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Failed to delete user');
    }
  };

  const resetForm = () => {
    setFormData({
      username: '',
      email: '',
      first_name: '',
      last_name: '',
      phone: '',
      position: '',
      institution: '',
      role: '',
      password: '',
      password_confirm: ''
    });
  };

  const openEditModal = (user: User) => {
    setEditingUser(user);
    setFormData({
      username: user.username,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      phone: user.phone || '',
      position: user.position || '',
      institution: user.institution || '',
      role: user.role.toString(),
      password: '',
      password_confirm: ''
    });
    setShowCreateModal(true);
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading admin dashboard...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      {/* Header Section */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="display-5 fw-bold text-primary">
                <i className="fas fa-tachometer-alt me-3"></i>
                Admin Dashboard
              </h1>
              <p className="lead text-muted">System overview and user management</p>
            </div>
            <div className="d-flex gap-2">
              <Link to="/events">
                <Button variant="outline-primary">
                  <i className="fas fa-calendar me-2"></i>
                  Manage Events
                </Button>
              </Link>
              <Button variant="primary" onClick={() => setShowCreateModal(true)}>
                <i className="fas fa-user-plus me-2"></i>
                Create User
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {/* System Statistics Cards */}
      <Row className="mb-4">
        <Col lg={2} md={4} sm={6} className="mb-3">
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-primary mb-2">
                <i className="fas fa-calendar-alt fa-2x"></i>
              </div>
              <h3 className="fw-bold text-primary">{systemStats.totalEvents}</h3>
              <p className="text-muted mb-0">Total Events</p>
              <small className="text-success">
                {systemStats.activeEvents} active
              </small>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={2} md={4} sm={6} className="mb-3">
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-success mb-2">
                <i className="fas fa-users fa-2x"></i>
              </div>
              <h3 className="fw-bold text-success">{systemStats.totalParticipants}</h3>
              <p className="text-muted mb-0">Participants</p>
              <small className="text-warning">
                {systemStats.pendingParticipants} pending
              </small>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={2} md={4} sm={6} className="mb-3">
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-info mb-2">
                <i className="fas fa-user-shield fa-2x"></i>
              </div>
              <h3 className="fw-bold text-info">{systemStats.totalUsers}</h3>
              <p className="text-muted mb-0">System Users</p>
              <small className="text-muted">
                Admin & Staff
              </small>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={2} md={4} sm={6} className="mb-3">
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-warning mb-2">
                <i className="fas fa-check-circle fa-2x"></i>
              </div>
              <h3 className="fw-bold text-warning">{systemStats.confirmedParticipants}</h3>
              <p className="text-muted mb-0">Confirmed</p>
              <small className="text-success">
                {systemStats.totalParticipants > 0 ? Math.round((systemStats.confirmedParticipants / systemStats.totalParticipants) * 100) : 0}% rate
              </small>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={2} md={4} sm={6} className="mb-3">
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-secondary mb-2">
                <i className="fas fa-chart-line fa-2x"></i>
              </div>
              <h3 className="fw-bold text-secondary">
                {systemStats.totalEvents > 0 ? Math.round(systemStats.totalParticipants / systemStats.totalEvents) : 0}
              </h3>
              <p className="text-muted mb-0">Avg/Event</p>
              <small className="text-muted">
                Participants
              </small>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={2} md={4} sm={6} className="mb-3">
          <Card className="border-0 shadow-sm h-100">
            <Card.Body className="text-center">
              <div className="text-danger mb-2">
                <i className="fas fa-cogs fa-2x"></i>
              </div>
              <h3 className="fw-bold text-danger">
                {systemStats.activeEvents}
              </h3>
              <p className="text-muted mb-0">Active Events</p>
              <small className="text-info">
                Running now
              </small>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Navigation Tabs */}
      <Tab.Container activeKey={activeTab} onSelect={(k) => setActiveTab(k || 'overview')}>
        <Nav variant="tabs" className="mb-4">
          <Nav.Item>
            <Nav.Link eventKey="overview">
              <i className="fas fa-chart-pie me-2"></i>
              System Overview
            </Nav.Link>
          </Nav.Item>
          <Nav.Item>
            <Nav.Link eventKey="users">
              <i className="fas fa-users me-2"></i>
              User Management ({users.length})
            </Nav.Link>
          </Nav.Item>
        </Nav>

        <Tab.Content>
          {/* Overview Tab */}
          <Tab.Pane eventKey="overview">
            <Row>
              <Col lg={8}>
                <Card className="mb-4">
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-chart-bar me-2"></i>
                      System Health & Performance
                    </h5>
                  </Card.Header>
                  <Card.Body>
                    <Row>
                      <Col md={6}>
                        <div className="mb-3">
                          <div className="d-flex justify-content-between mb-1">
                            <span>Event Capacity Utilization</span>
                            <span>{systemStats.totalEvents > 0 ? Math.round((systemStats.activeEvents / systemStats.totalEvents) * 100) : 0}%</span>
                          </div>
                          <ProgressBar
                            now={systemStats.totalEvents > 0 ? (systemStats.activeEvents / systemStats.totalEvents) * 100 : 0}
                            variant="primary"
                          />
                        </div>
                        <div className="mb-3">
                          <div className="d-flex justify-content-between mb-1">
                            <span>Participant Confirmation Rate</span>
                            <span>{systemStats.totalParticipants > 0 ? Math.round((systemStats.confirmedParticipants / systemStats.totalParticipants) * 100) : 0}%</span>
                          </div>
                          <ProgressBar
                            now={systemStats.totalParticipants > 0 ? (systemStats.confirmedParticipants / systemStats.totalParticipants) * 100 : 0}
                            variant="success"
                          />
                        </div>
                      </Col>
                      <Col md={6}>
                        <div className="mb-3">
                          <div className="d-flex justify-content-between mb-1">
                            <span>System User Activity</span>
                            <span>85%</span>
                          </div>
                          <ProgressBar now={85} variant="info" />
                        </div>
                        <div className="mb-3">
                          <div className="d-flex justify-content-between mb-1">
                            <span>Database Performance</span>
                            <span>92%</span>
                          </div>
                          <ProgressBar now={92} variant="warning" />
                        </div>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>
              </Col>
              <Col lg={4}>
                <Card className="mb-4">
                  <Card.Header>
                    <h5 className="mb-0">
                      <i className="fas fa-tasks me-2"></i>
                      Quick Actions
                    </h5>
                  </Card.Header>
                  <Card.Body className="d-grid gap-2">
                    <Link to="/events/new">
                      <Button variant="outline-primary" className="w-100">
                        <i className="fas fa-plus me-2"></i>
                        Create New Event
                      </Button>
                    </Link>
                    <Link to="/participants/manage">
                      <Button variant="outline-success" className="w-100">
                        <i className="fas fa-users me-2"></i>
                        Manage Participants
                      </Button>
                    </Link>
                    <Link to="/hotels">
                      <Button variant="outline-info" className="w-100">
                        <i className="fas fa-hotel me-2"></i>
                        Manage Hotels
                      </Button>
                    </Link>
                    <Link to="/drivers">
                      <Button variant="outline-warning" className="w-100">
                        <i className="fas fa-car me-2"></i>
                        Manage Drivers
                      </Button>
                    </Link>
                    <Link to="/organizations">
                      <Button variant="outline-dark" className="w-100">
                        <i className="fas fa-building me-2"></i>
                        Manage Organizations
                      </Button>
                    </Link>
                    <Button variant="outline-secondary" onClick={() => setShowCreateModal(true)}>
                      <i className="fas fa-user-plus me-2"></i>
                      Add System User
                    </Button>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </Tab.Pane>

          {/* Users Tab */}
          <Tab.Pane eventKey="users">
            {error && (
              <Alert variant="danger" dismissible onClose={() => setError('')}>
                <i className="fas fa-exclamation-triangle me-2"></i>
                {error}
              </Alert>
            )}

            {/* Users Table */}
            <Card className="shadow-sm">
              <Card.Header className="bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 className="mb-0">
                  <i className="fas fa-users me-2"></i>
                  System Users ({users.length})
                </h5>
                <Button variant="light" size="sm" onClick={() => setShowCreateModal(true)}>
                  <i className="fas fa-user-plus me-2"></i>
                  Add User
                </Button>
              </Card.Header>
            <Card.Body className="p-0">
              <Table responsive striped hover className="mb-0">
                <thead className="bg-light">
                  <tr>
                    <th>User</th>
                    <th>Role</th>
                    <th>Contact</th>
                    <th>Status</th>
                    <th>Last Login</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <div className="me-3">
                            <div 
                              className="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                              style={{ 
                                width: '40px', 
                                height: '40px', 
                                backgroundColor: user.role_color 
                              }}
                            >
                              {user.first_name[0]}{user.last_name[0]}
                            </div>
                          </div>
                          <div>
                            <div className="fw-bold">{user.full_name}</div>
                            <small className="text-muted">@{user.username}</small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <Badge 
                          style={{ backgroundColor: user.role_color }}
                          className="px-3 py-2"
                        >
                          {user.role_display_name}
                        </Badge>
                      </td>
                      <td>
                        <div>
                          <div className="small">
                            <i className="fas fa-envelope me-1"></i>
                            {user.email}
                          </div>
                          {user.phone && (
                            <div className="small text-muted">
                              <i className="fas fa-phone me-1"></i>
                              {user.phone}
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <Badge bg={user.is_active ? 'success' : 'danger'}>
                          {user.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        {user.is_staff && (
                          <Badge bg="info" className="ms-1">Staff</Badge>
                        )}
                        {user.is_superuser && (
                          <Badge bg="warning" className="ms-1">Super</Badge>
                        )}
                      </td>
                      <td>
                        {user.last_login ? (
                          <small>
                            {new Date(user.last_login).toLocaleDateString()}
                          </small>
                        ) : (
                          <small className="text-muted">Never</small>
                        )}
                      </td>
                      <td>
                        <div className="d-flex gap-2">
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => openEditModal(user)}
                          >
                            <i className="fas fa-edit"></i>
                          </Button>
                          {user.id !== user?.id && (
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleDeleteUser(user.id)}
                            >
                              <i className="fas fa-trash"></i>
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>
          </Tab.Pane>
        </Tab.Content>
      </Tab.Container>

      {/* Create/Edit User Modal */}
      <Modal show={showCreateModal} onHide={() => {
        setShowCreateModal(false);
        setEditingUser(null);
        resetForm();
      }} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {editingUser ? 'Edit User' : 'Create New User'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={editingUser ? handleUpdateUser : handleCreateUser}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Username</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.username}
                    onChange={(e) => setFormData({...formData, username: e.target.value})}
                    required
                    disabled={!!editingUser}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>First Name</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.first_name}
                    onChange={(e) => setFormData({...formData, first_name: e.target.value})}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Last Name</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.last_name}
                    onChange={(e) => setFormData({...formData, last_name: e.target.value})}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Role</Form.Label>
                  <Form.Select
                    value={formData.role}
                    onChange={(e) => setFormData({...formData, role: e.target.value})}
                    required
                  >
                    <option value="">Select Role</option>
                    {roles.map((role) => (
                      <option key={role.id} value={role.id}>
                        {role.display_name}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Phone</Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                  />
                </Form.Group>
              </Col>
            </Row>

            {!editingUser && (
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Password</Form.Label>
                    <Form.Control
                      type="password"
                      value={formData.password}
                      onChange={(e) => setFormData({...formData, password: e.target.value})}
                      required={!editingUser}
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Confirm Password</Form.Label>
                    <Form.Control
                      type="password"
                      value={formData.password_confirm}
                      onChange={(e) => setFormData({...formData, password_confirm: e.target.value})}
                      required={!editingUser}
                    />
                  </Form.Group>
                </Col>
              </Row>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => {
              setShowCreateModal(false);
              setEditingUser(null);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              {editingUser ? 'Update User' : 'Create User'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default AdminDashboard;

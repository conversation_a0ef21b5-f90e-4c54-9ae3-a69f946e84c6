from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import ParticipantViewSet, ParticipantTypeViewSet, AttendanceViewSet, VisitingInterestViewSet, check_email_exists

router = DefaultRouter()
router.register(r'participants', ParticipantViewSet)
router.register(r'participant-types', ParticipantTypeViewSet)
router.register(r'visiting-interests', VisitingInterestViewSet)
router.register(r'attendance', AttendanceViewSet)

urlpatterns = [
    path('check-email/', check_email_exists, name='check-email-exists'),
    path('', include(router.urls)),
]

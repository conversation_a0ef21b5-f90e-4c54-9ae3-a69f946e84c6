import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api';
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // For FormData, let the browser set the Content-Type header
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Types
export interface Event {
  id: number;
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  location: string;
  city: string;
  country: string;
  latitude?: number;
  longitude?: number;
  organizer_name: string;
  organizer_email: string;
  organizer_phone: string;
  logo?: string;
  banner?: string;
  is_active: boolean;
  participant_count: number;
  created_at: string;
  updated_at: string;
}

export interface EventSchedule {
  id: number;
  event: number;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  location: string;
  speaker: string;
  is_break: boolean;
  created_at: string;
}

export interface EventGallery {
  id: number;
  event: number;
  title: string;
  description: string;
  image: string;
  uploaded_at: string;
  is_featured: boolean;
}

export interface ParticipantType {
  id: number;
  name: string;
  description: string;
  color: string;
  created_at: string;
}

export interface Participant {
  id: number;
  uuid: string;
  first_name: string;
  last_name: string;
  middle_name: string;
  email: string;
  phone: string;
  institution_name: string;
  position: string;
  event: number;
  participant_type: number;
  arrival_date: string;
  departure_date: string;
  profile_photo: string;
  badge_generated: boolean;
  badge_file?: string;
  remarks: string;
  registration_date: string;
  is_confirmed: boolean;
  full_name: string;
  participant_type_name: string;
  participant_type_color: string;
  event_name: string;
  badge_url?: string;
  // Assignment fields
  assigned_hotel?: number;
  assigned_driver?: number;
  assigned_hotel_name?: string;
  assigned_driver_name?: string;
  // Hotel details
  assigned_hotel_address?: string;
  assigned_hotel_phone?: string;
  assigned_hotel_email?: string;
  // Driver details
  assigned_driver_phone?: string;
  assigned_driver_car_model?: string;
  assigned_driver_car_plate?: string;
}

export interface VisitingInterest {
  id: number;
  name: string;
  description: string;
  location: string;
  event: number;
  is_active: boolean;
  max_participants?: number;
  current_participants_count: number;
  is_full: boolean;
  created_at: string;
  updated_at: string;
}

export interface ParticipantVisitingInterest {
  id: number;
  visiting_interest: number;
  visiting_interest_name: string;
  visiting_interest_description: string;
  visiting_interest_location: string;
  priority: number;
  selected_at: string;
}

export interface ParticipantRegistration {
  first_name: string;
  last_name: string;
  middle_name?: string;
  email: string;
  phone: string;
  institution_name: string;
  position: string;
  event: number;
  participant_type: number;
  arrival_date: string;
  departure_date: string;
  profile_photo: File;
  remarks?: string;
  visiting_interests?: number[];
}

// Hotel interfaces
export interface Hotel {
  id: number;
  name: string;
  address: string;
  phone: string;
  email: string;
  contact_person: string;
  latitude?: number;
  longitude?: number;
  star_rating?: number;
  website?: string;
  description: string;
  event: number;
  event_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  available_rooms_count: number;
}



// Driver interfaces
export interface Driver {
  id: number;
  name: string;
  phone: string;
  email: string;
  photo?: string;
  car_plate: string;
  car_code: string;
  car_model: string;
  car_color: string;
  license_number: string;
  is_available: boolean;
  notes: string;
  event: number;
  event_name: string;
  created_at: string;
  updated_at: string;
}

export interface DriverAssignment {
  id: number;
  driver: number;
  participant: number;
  pickup_location: string;
  destination: string;
  pickup_time: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  notes: string;
  created_at: string;
  updated_at: string;
  driver_name?: string;
  driver_phone?: string;
  driver_car_plate?: string;
  participant_name?: string;
  participant_phone?: string;
}

export interface Developer {
  id: number;
  full_name: string;
  profession: string;
  linkedin_link?: string;
  photo?: string;
  photo_url?: string;
  bio?: string;
  order: number;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// API Services
export const eventService = {
  getEvents: () => api.get<PaginatedResponse<Event>>('/events/'),
  getEvent: (id: number) => api.get<Event>(`/events/${id}/`),
  createEvent: (data: FormData | Partial<Event>) => api.post<Event>('/events/', data),
  updateEvent: (id: number, data: FormData | Partial<Event>) => api.put<Event>(`/events/${id}/`, data),
  deleteEvent: (id: number) => api.delete(`/events/${id}/`),
  getEventSchedule: (id: number) => api.get<EventSchedule[]>(`/events/${id}/schedule/`),
  getEventGallery: (id: number) => api.get<EventGallery[]>(`/events/${id}/gallery/`),
  getEventParticipants: (id: number) => api.get<Participant[]>(`/events/${id}/participants/`),
  // Public endpoints
  getPublicEvents: () => api.get<PaginatedResponse<Event>>('/public/events/'),
  getPublicEvent: (id: number) => api.get<Event>(`/public/events/${id}/`),
};

export const participantService = {
  getParticipants: (eventId?: number) => {
    const params = eventId ? { event: eventId } : {};
    return api.get<Participant[]>('/participants/', { params });
  },
  getParticipant: (id: number) => api.get<Participant>(`/participants/${id}/`),
  registerParticipant: (data: FormData) => api.post<Participant>('/participants/', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  updateParticipant: (id: number, data: FormData) => api.put<Participant>(`/participants/${id}/`, data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  deleteParticipant: (id: number) => api.delete(`/participants/${id}/`),
  confirmParticipant: (id: number) => api.post(`/participants/${id}/confirm/`),
  assignHotel: (id: number, hotelId: number) => api.post(`/participants/${id}/assign_hotel/`, { hotel_id: hotelId }),
  assignDriver: (id: number, driverId: number) => api.post(`/participants/${id}/assign_driver/`, { driver_id: driverId }),
  getParticipantBadge: (id: number) => api.get(`/participants/${id}/badge/`),
  regenerateBadge: (id: number) => api.post(`/participants/${id}/regenerate_badge/`),
  verifyParticipant: (uuid: string) => api.get(`/participants/verify/?uuid=${uuid}`),
};

export const participantTypeService = {
  getParticipantTypes: () => api.get<ParticipantType[]>('/participant-types/'),
  getParticipantType: (id: number) => api.get<ParticipantType>(`/participant-types/${id}/`),
  createParticipantType: (data: Partial<ParticipantType>) => api.post<ParticipantType>('/participant-types/', data),
  updateParticipantType: (id: number, data: Partial<ParticipantType>) => api.put<ParticipantType>(`/participant-types/${id}/`, data),
  deleteParticipantType: (id: number) => api.delete(`/participant-types/${id}/`),
};

export const visitingInterestService = {
  getVisitingInterests: (eventId?: number) => {
    const params = eventId ? { event: eventId } : {};
    return api.get<VisitingInterest[]>('/visiting-interests/', { params });
  },
  getVisitingInterest: (id: number) => api.get<VisitingInterest>(`/visiting-interests/${id}/`),
  createVisitingInterest: (data: Partial<VisitingInterest>) => api.post<VisitingInterest>('/visiting-interests/', data),
  updateVisitingInterest: (id: number, data: Partial<VisitingInterest>) => api.put<VisitingInterest>(`/visiting-interests/${id}/`, data),
  deleteVisitingInterest: (id: number) => api.delete(`/visiting-interests/${id}/`),
};

export const attendanceService = {
  checkIn: (data: {
    uuid: string;
    event_schedule_id: number;
    checked_in_by?: string;
    notes?: string;
  }) => api.post('/attendance/check_in/', data),
  getAttendance: (participantId?: number, eventScheduleId?: number) => {
    const params: any = {};
    if (participantId) params.participant = participantId;
    if (eventScheduleId) params.event_schedule = eventScheduleId;
    return api.get('/attendance/', { params });
  },
};

// Hotel services
export const hotelService = {
  getHotels: (eventId?: number, isActive?: boolean) => {
    const params: any = {};
    if (eventId) params.event = eventId;
    if (isActive !== undefined) params.active = isActive;
    return api.get<Hotel[]>('/hotels/', { params });
  },
  getHotel: (id: number) => api.get<Hotel>(`/hotels/${id}/`),
  createHotel: (data: Partial<Hotel>) => api.post<Hotel>('/hotels/', data),
  updateHotel: (id: number, data: Partial<Hotel>) => api.put<Hotel>(`/hotels/${id}/`, data),
  deleteHotel: (id: number) => api.delete(`/hotels/${id}/`),

};



// Driver services
export const driverService = {
  getDrivers: (eventId?: number, isAvailable?: boolean) => {
    const params: any = {};
    if (eventId) params.event = eventId;
    if (isAvailable !== undefined) params.available = isAvailable;
    return api.get<Driver[]>('/drivers/', { params });
  },
  getDriver: (id: number) => api.get<Driver>(`/drivers/${id}/`),
  createDriver: (data: FormData) => api.post<Driver>('/drivers/', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  updateDriver: (id: number, data: FormData) => api.put<Driver>(`/drivers/${id}/`, data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  deleteDriver: (id: number) => api.delete(`/drivers/${id}/`),
  toggleAvailability: (id: number) => api.post(`/drivers/${id}/toggle_availability/`),
};

export const driverAssignmentService = {
  getAssignments: (driverId?: number, participantId?: number, status?: string) => {
    const params: any = {};
    if (driverId) params.driver = driverId;
    if (participantId) params.participant = participantId;
    if (status) params.status = status;
    return api.get<DriverAssignment[]>('/driver-assignments/', { params });
  },
  getAssignment: (id: number) => api.get<DriverAssignment>(`/driver-assignments/${id}/`),
  createAssignment: (data: Partial<DriverAssignment>) => api.post<DriverAssignment>('/driver-assignments/', data),
  updateAssignment: (id: number, data: Partial<DriverAssignment>) => api.put<DriverAssignment>(`/driver-assignments/${id}/`, data),
  deleteAssignment: (id: number) => api.delete(`/driver-assignments/${id}/`),
  startTrip: (id: number) => api.post(`/driver-assignments/${id}/start/`),
  completeTrip: (id: number) => api.post(`/driver-assignments/${id}/complete/`),
  cancelAssignment: (id: number) => api.post(`/driver-assignments/${id}/cancel/`),
};

// Organization interfaces
export interface Organization {
  id: number;
  name: string;
  short_name: string;
  logo?: string;
  motto: string;
  description: string;
  email: string;
  phone: string;
  website: string;
  address_line_1: string;
  address_line_2: string;
  city: string;
  state_province: string;
  postal_code: string;
  country: string;
  full_address: string;
  facebook_url: string;
  twitter_url: string;
  linkedin_url: string;
  instagram_url: string;
  is_active: boolean;
  is_primary: boolean;
  display_name: string;
  created_at: string;
  updated_at: string;
  settings?: OrganizationSettings;
}

export interface OrganizationSettings {
  id: number;
  default_event_duration_hours: number;
  default_registration_fee: number;
  email_signature: string;
  primary_color: string;
  secondary_color: string;
  send_welcome_emails: boolean;
  send_confirmation_emails: boolean;
  send_reminder_emails: boolean;
  created_at: string;
  updated_at: string;
}

// Organization services
export const organizationService = {
  getOrganizations: (isActive?: boolean) => {
    const params: any = {};
    if (isActive !== undefined) params.is_active = isActive;
    return api.get<Organization[]>('/organizations/', { params });
  },
  getOrganization: (id: number) => api.get<Organization>(`/organizations/${id}/`),
  createOrganization: (data: FormData | Partial<Organization>) => api.post<Organization>('/organizations/', data),
  updateOrganization: (id: number, data: FormData | Partial<Organization>) => api.put<Organization>(`/organizations/${id}/`, data),
  deleteOrganization: (id: number) => api.delete(`/organizations/${id}/`),
  getPrimaryOrganization: () => api.get<Organization>('/organizations/primary/'),
  setPrimaryOrganization: (id: number) => api.post(`/organizations/${id}/set_primary/`),
  toggleActive: (id: number) => api.post(`/organizations/${id}/toggle_active/`),
  getSettings: (id: number) => api.get<OrganizationSettings>(`/organizations/${id}/organization_settings/`),
  updateSettings: (id: number, data: Partial<OrganizationSettings>) => api.put<OrganizationSettings>(`/organizations/${id}/organization_settings/`, data),
  // Public endpoints
  getPublicOrganizations: () => api.get<PaginatedResponse<Organization>>('/public/organizations/'),
  getPublicOrganization: (id: number) => api.get<Organization>(`/public/organizations/${id}/`),
};

export const developerService = {
  getDevelopers: () => api.get<PaginatedResponse<Developer>>('/auth/developers/'),
};

// Helper function to get full URL for media files
export const getMediaUrl = (path: string): string => {
  if (!path) return '';
  if (path.startsWith('http')) return path;
  return `${BACKEND_URL}${path}`;
};

export default api;

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.http import HttpResponse
from .models import Badge, BadgeTemplate
from .serializers import BadgeSerializer, BadgeTemplateSerializer


class BadgeTemplateViewSet(viewsets.ModelViewSet):
    queryset = BadgeTemplate.objects.all()
    serializer_class = BadgeTemplateSerializer

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set this template as default"""
        template = self.get_object()
        template.is_default = True
        template.save()

        serializer = self.get_serializer(template)
        return Response(serializer.data)


class BadgeViewSet(viewsets.ModelViewSet):
    queryset = Badge.objects.all()
    serializer_class = BadgeSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        participant_id = self.request.query_params.get('participant', None)
        event_id = self.request.query_params.get('event', None)
        is_generated = self.request.query_params.get('generated', None)

        if participant_id is not None:
            queryset = queryset.filter(participant=participant_id)
        if event_id is not None:
            queryset = queryset.filter(participant__event=event_id)
        if is_generated is not None:
            queryset = queryset.filter(is_generated=is_generated.lower() == 'true')

        return queryset

    @action(detail=True, methods=['post'])
    def regenerate(self, request, pk=None):
        """Regenerate badge"""
        badge = self.get_object()
        try:
            badge.generate_badge()
            serializer = self.get_serializer(badge)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download badge image"""
        badge = self.get_object()
        if badge.badge_image:
            response = HttpResponse(badge.badge_image.read(), content_type='image/png')
            response['Content-Disposition'] = f'attachment; filename="badge_{badge.participant.uuid}.png"'
            return response
        else:
            return Response({'error': 'Badge not generated'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def bulk_generate(self, request):
        """Generate badges for multiple participants"""
        participant_ids = request.data.get('participant_ids', [])
        if not participant_ids:
            return Response({'error': 'No participant IDs provided'},
                          status=status.HTTP_400_BAD_REQUEST)

        generated_badges = []
        errors = []

        for participant_id in participant_ids:
            try:
                from participants.models import Participant
                participant = Participant.objects.get(id=participant_id)
                badge, created = Badge.objects.get_or_create(participant=participant)
                badge.generate_badge()
                generated_badges.append(badge.id)
            except Exception as e:
                errors.append(f"Participant {participant_id}: {str(e)}")

        return Response({
            'generated_badges': generated_badges,
            'errors': errors,
            'total_generated': len(generated_badges),
            'total_errors': len(errors)
        })

# University of Gondar Event Management System - Local Deployment Script (PowerShell)

param(
    [switch]$Rebuild,
    [switch]$Logs
)

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Blue = "Cyan"

Write-Host "========================================" -ForegroundColor $Blue
Write-Host "UoG Event Management - Local Deployment" -ForegroundColor $Blue
Write-Host "========================================" -ForegroundColor $Blue

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor $Green
} catch {
    Write-Host "✗ Error: Docker is not running. Please start Docker Desktop and try again." -ForegroundColor $Red
    exit 1
}

# Check if Docker Compose is available
try {
    docker-compose --version | Out-Null
    Write-Host "✓ Docker Compose is available" -ForegroundColor $Green
} catch {
    Write-Host "✗ Error: Docker Compose is not installed." -ForegroundColor $Red
    exit 1
}

# Load environment variables
if (Test-Path ".env.local") {
    Write-Host "Loading local environment variables..." -ForegroundColor $Yellow
    Get-Content ".env.local" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
} else {
    Write-Host "Creating .env.local from template..." -ForegroundColor $Yellow
    if (Test-Path ".env.local.template") {
        Copy-Item ".env.local.template" ".env.local"
        Write-Host "Please edit .env.local with your configuration and run this script again." -ForegroundColor $Yellow
        exit 1
    } else {
        Write-Host "Warning: No .env.local or template found. Using defaults." -ForegroundColor $Yellow
    }
}

# Stop existing containers
Write-Host "Stopping existing containers..." -ForegroundColor $Yellow
docker-compose -f docker-compose.local.yml down

# Build images
if ($Rebuild) {
    Write-Host "Rebuilding images..." -ForegroundColor $Yellow
    docker-compose -f docker-compose.local.yml build --no-cache
} else {
    Write-Host "Building images..." -ForegroundColor $Yellow
    docker-compose -f docker-compose.local.yml build
}

# Start services
Write-Host "Starting services..." -ForegroundColor $Yellow
docker-compose -f docker-compose.local.yml up -d

# Wait for services to be ready
Write-Host "Waiting for services to be ready..." -ForegroundColor $Yellow
Start-Sleep -Seconds 15

# Check service health
Write-Host "Checking service health..." -ForegroundColor $Yellow

# Check database
try {
    docker-compose -f docker-compose.local.yml exec -T db pg_isready -U postgres | Out-Null
    Write-Host "✓ Database is ready" -ForegroundColor $Green
} catch {
    Write-Host "✗ Database is not ready" -ForegroundColor $Red
}

# Check Redis
try {
    docker-compose -f docker-compose.local.yml exec -T redis redis-cli ping | Out-Null
    Write-Host "✓ Redis is ready" -ForegroundColor $Green
} catch {
    Write-Host "✗ Redis is not ready" -ForegroundColor $Red
}

# Check backend
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/admin/login/" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Backend is ready" -ForegroundColor $Green
    }
} catch {
    Write-Host "✗ Backend is not ready" -ForegroundColor $Red
}

# Check frontend
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Frontend is ready" -ForegroundColor $Green
    }
} catch {
    Write-Host "✗ Frontend is not ready" -ForegroundColor $Red
}

Write-Host "========================================" -ForegroundColor $Blue
Write-Host "Local deployment completed!" -ForegroundColor $Green
Write-Host "========================================" -ForegroundColor $Blue
Write-Host "Services:" -ForegroundColor $Yellow
Write-Host "Frontend: http://localhost:3001" -ForegroundColor $Blue
Write-Host "Backend API: http://localhost:8000/api" -ForegroundColor $Blue
Write-Host "Admin Panel: http://localhost:8000/admin" -ForegroundColor $Blue
Write-Host "Database: localhost:5432" -ForegroundColor $Blue
Write-Host "Redis: localhost:6379" -ForegroundColor $Blue
Write-Host "========================================" -ForegroundColor $Blue

# Show logs if requested
if ($Logs) {
    Write-Host "Showing logs..." -ForegroundColor $Yellow
    docker-compose -f docker-compose.local.yml logs -f
} else {
    $showLogs = Read-Host "Do you want to view logs? (y/N)"
    if ($showLogs -eq "y" -or $showLogs -eq "Y") {
        docker-compose -f docker-compose.local.yml logs -f
    }
}

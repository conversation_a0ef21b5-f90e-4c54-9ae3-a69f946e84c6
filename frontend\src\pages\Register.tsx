import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { eventService, Event } from '../services/api';
import LandingNavbar from '../components/LandingNavbar';
import Footer from '../components/Footer';
import '../styles/landing.css';

const Register: React.FC = () => {
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const response = await eventService.getEvents();
        // Handle paginated response
        const eventsData = response.data.results || [];
        const upcomingEvents = eventsData.filter((event: Event) => new Date(event.start_date) > new Date());
        setEvents(upcomingEvents);
      } catch (error) {
        console.error('Error fetching events:', error);
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };
    fetchEvents();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="container-fluid py-5">
        <div className="row justify-content-center">
          <div className="col-md-6 text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3">Loading registration form...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="register-page">
      {/* Navigation Menu */}
      <LandingNavbar />

      {/* Header */}
      <div className="register-header py-5 position-relative overflow-hidden" style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        marginTop: '80px'
      }}>
        {/* Particles Background */}
        <div className="particles position-absolute w-100 h-100">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="particle position-absolute rounded-circle bg-white"
              style={{
                width: Math.random() * 4 + 2 + 'px',
                height: Math.random() * 4 + 2 + 'px',
                left: Math.random() * 100 + '%',
                top: Math.random() * 100 + '%',
                opacity: Math.random() * 0.5 + 0.1,
                animation: `float ${Math.random() * 3 + 2}s ease-in-out infinite alternate`
              }}
            />
          ))}
        </div>

        <div className="container position-relative">
          <div className="row">
            <div className="col-lg-8 mx-auto text-center text-white">
              <h1 className="display-4 fw-bold mb-4 hero-title">Event Registration</h1>
              <p className="lead hero-description">
                Register for upcoming conferences and academic events at the University of Gondar
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-lg-10">
            {events.length > 0 ? (
              <div className="registration-form hero-glass-panel rounded-4 p-5" style={{
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)'
              }}>
                <div className="text-center mb-5">
                  <h2 className="display-6 fw-bold mb-3 text-primary">Select an Event to Register</h2>
                  <p className="lead text-muted">Choose from our upcoming academic events and conferences</p>
                </div>
                
                {/* Event Selection */}
                <div className="mb-4">
                  <label className="form-label fw-bold">Available Events</label>
                  <div className="row g-3">
                    {events.map((event) => (
                      <div key={event.id} className="col-12">
                        <div 
                          className={`event-option p-4 border rounded-3 cursor-pointer ${
                            selectedEvent === event.id ? 'border-primary bg-primary bg-opacity-10' : 'border-light'
                          }`}
                          onClick={() => setSelectedEvent(event.id)}
                          style={{ cursor: 'pointer' }}
                        >
                          <div className="d-flex align-items-start">
                            <input
                              type="radio"
                              name="event"
                              value={event.id}
                              checked={selectedEvent === event.id}
                              onChange={() => setSelectedEvent(event.id)}
                              className="form-check-input me-3 mt-1"
                            />
                            <div className="flex-grow-1">
                              <h5 className="mb-2">{event.name}</h5>
                              <p className="text-muted mb-2" style={{ fontSize: '0.9rem' }}>
                                {event.description.length > 150 
                                  ? event.description.substring(0, 150) + '...' 
                                  : event.description
                                }
                              </p>
                              <div className="row g-2">
                                <div className="col-md-6">
                                  <small className="text-muted">
                                    <i className="fas fa-calendar-alt me-1"></i>
                                    {formatDate(event.start_date)} - {formatDate(event.end_date)}
                                  </small>
                                </div>
                                <div className="col-md-6">
                                  <small className="text-muted">
                                    <i className="fas fa-map-marker-alt me-1"></i>
                                    {event.location}
                                  </small>
                                </div>
                              </div>
                              <small className="text-info">
                                <i className="fas fa-users me-1"></i>
                                {event.participant_count} participants registered
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {selectedEvent && (
                  <div className="registration-notice bg-light rounded-3 p-4 mb-4">
                    <h5 className="text-primary mb-3">
                      <i className="fas fa-info-circle me-2"></i>
                      Registration Information
                    </h5>
                    <p className="mb-3">
                      To complete your registration for this event, you'll need to:
                    </p>
                    <ul className="list-unstyled">
                      <li className="mb-2">
                        <i className="fas fa-check text-success me-2"></i>
                        Create an account or log in to the event management system
                      </li>
                      <li className="mb-2">
                        <i className="fas fa-check text-success me-2"></i>
                        Fill out your participant information
                      </li>
                      <li className="mb-2">
                        <i className="fas fa-check text-success me-2"></i>
                        Upload required documents (if any)
                      </li>
                      <li className="mb-2">
                        <i className="fas fa-check text-success me-2"></i>
                        Receive confirmation and event details
                      </li>
                    </ul>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="d-flex gap-3 justify-content-center">
                  <Link
                    to="/"
                    className="btn btn-outline-secondary btn-lg px-4"
                  >
                    <i className="fas fa-arrow-left me-2"></i>
                    Back to Home
                  </Link>
                  
                  <Link
                    to="/participant-register"
                    className="btn btn-primary btn-lg px-5"
                  >
                    <i className="fas fa-user-plus me-2"></i>
                    Continue to Registration
                  </Link>
                </div>
              </div>
            ) : (
              <div className="no-events text-center py-5">
                <div className="bg-white rounded-4 shadow-lg p-5">
                  <i className="fas fa-calendar-times fa-4x text-muted mb-4"></i>
                  <h3 className="text-muted mb-3">No Upcoming Events</h3>
                  <p className="text-muted mb-4">
                    There are currently no upcoming events available for registration. 
                    Please check back later or contact the event organizers for more information.
                  </p>
                  <div className="d-flex gap-3 justify-content-center">
                    <Link
                      to="/"
                      className="btn btn-primary btn-lg px-4"
                    >
                      <i className="fas fa-home me-2"></i>
                      Back to Home
                    </Link>
                    <Link
                      to="/events"
                      className="btn btn-outline-primary btn-lg px-4"
                    >
                      <i className="fas fa-calendar me-2"></i>
                      View All Events
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Register;

from rest_framework import serializers
from .models import Badge, BadgeTemplate


class BadgeTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = BadgeTemplate
        fields = '__all__'


class BadgeSerializer(serializers.ModelSerializer):
    participant_name = serializers.CharField(source='participant.full_name', read_only=True)
    participant_email = serializers.CharField(source='participant.email', read_only=True)
    participant_type = serializers.CharField(source='participant.participant_type.name', read_only=True)
    participant_type_color = serializers.CharField(source='participant.participant_type.color', read_only=True)
    event_name = serializers.CharField(source='participant.event.name', read_only=True)
    template_name = serializers.CharField(source='template.name', read_only=True)

    class Meta:
        model = Badge
        fields = '__all__'

#!/usr/bin/env python
"""
<PERSON>ript to create sample data for testing the Event Management System
WARNING: This script creates test data and should not be used in production
"""
import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from events.models import Event
from participants.models import ParticipantType
from hotels.models import Hotel, HotelRoom
from drivers.models import Driver


def create_sample_data():
    print("WARNING: This script creates test data and should not be used in production!")
    print("Creating sample data...")
    
    # Create sample event
    event, created = Event.objects.get_or_create(
        name="Annual Tech Conference 2025",
        defaults={
            'description': 'A comprehensive technology conference featuring the latest innovations and trends.',
            'start_date': timezone.now() + timedelta(days=30),
            'end_date': timezone.now() + timedelta(days=32),
            'location': 'University of Gujrat, Main Campus',
            'city': 'Gujrat',
            'country': 'Pakistan',
            'organizer_name': 'University of Gujrat',
            'organizer_email': '<EMAIL>',
            'organizer_phone': '+92-53-1234567',
            'is_active': True
        }
    )
    if created:
        print(f"✓ Created event: {event.name}")
    else:
        print(f"✓ Event already exists: {event.name}")
    
    # Create participant types
    participant_types_data = [
        {'name': 'Speaker', 'color': '#dc3545'},
        {'name': 'Attendee', 'color': '#007bff'},
        {'name': 'VIP Guest', 'color': '#ffc107'},
        {'name': 'Student', 'color': '#28a745'},
        {'name': 'Faculty', 'color': '#6f42c1'},
    ]
    
    for pt_data in participant_types_data:
        pt, created = ParticipantType.objects.get_or_create(
            name=pt_data['name'],
            defaults={'color': pt_data['color']}
        )
        if created:
            print(f"✓ Created participant type: {pt.name}")
    
    # Create sample hotels
    hotels_data = [
        {
            'name': 'Grand Hotel Gujrat',
            'address': '123 Main Street, Gujrat, Punjab, Pakistan',
            'phone': '+92-53-1234567',
            'email': '<EMAIL>',
            'contact_person': 'Ahmed Ali Khan',
            'star_rating': 4,
            'website': 'https://grandhotelgujrat.com',
            'description': 'A luxury hotel in the heart of Gujrat city with modern amenities.',
            'latitude': 32.5734,
            'longitude': 74.0788,
        },
        {
            'name': 'City Inn Gujrat',
            'address': '456 University Road, Gujrat, Punjab, Pakistan',
            'phone': '+92-53-7654321',
            'email': '<EMAIL>',
            'contact_person': 'Sara Khan',
            'star_rating': 3,
            'website': 'https://cityinngujrat.com',
            'description': 'Budget-friendly hotel near the university campus.',
            'latitude': 32.5689,
            'longitude': 74.0812,
        },
        {
            'name': 'Royal Palace Hotel',
            'address': '789 GT Road, Gujrat, Punjab, Pakistan',
            'phone': '+92-53-9876543',
            'email': '<EMAIL>',
            'contact_person': 'Muhammad Hassan',
            'star_rating': 5,
            'website': 'https://royalpalacegujrat.com',
            'description': 'Premium 5-star hotel with world-class facilities.',
            'latitude': 32.5756,
            'longitude': 74.0756,
        }
    ]
    
    for hotel_data in hotels_data:
        hotel, created = Hotel.objects.get_or_create(
            name=hotel_data['name'],
            defaults={
                **hotel_data,
                'event': event,
                'is_active': True
            }
        )
        if created:
            print(f"✓ Created hotel: {hotel.name}")
            
            # Create sample rooms for each hotel
            room_types = ['Single', 'Double', 'Suite', 'Deluxe']
            for i in range(1, 11):  # 10 rooms per hotel
                room_type = room_types[i % len(room_types)]
                capacity = 1 if room_type == 'Single' else 2 if room_type == 'Double' else 4
                price = 5000 if room_type == 'Single' else 8000 if room_type == 'Double' else 15000 if room_type == 'Suite' else 12000
                
                room, room_created = HotelRoom.objects.get_or_create(
                    hotel=hotel,
                    room_number=f"{hotel.name[:3].upper()}-{i:03d}",
                    defaults={
                        'room_type': room_type,
                        'capacity': capacity,
                        'price_per_night': price,
                        'amenities': f'{room_type} room with modern amenities, AC, WiFi, TV',
                        'is_available': True
                    }
                )
                if room_created:
                    print(f"  ✓ Created room: {room.room_number}")
    
    # Create sample drivers
    drivers_data = [
        {
            'name': 'Muhammad Tariq',
            'phone': '+92-300-1234567',
            'email': '<EMAIL>',
            'car_plate': 'GJT-123',
            'car_code': 'CAR001',
            'car_model': 'Toyota Corolla',
            'car_color': 'White',
            'license_number': 'DL123456789',
            'notes': 'Experienced driver with 10+ years of service. Excellent knowledge of local routes.'
        },
        {
            'name': 'Ali Hassan',
            'phone': '+92-301-7654321',
            'email': '<EMAIL>',
            'car_plate': 'GJT-456',
            'car_code': 'CAR002',
            'car_model': 'Honda Civic',
            'car_color': 'Blue',
            'license_number': 'DL987654321',
            'notes': 'Reliable driver specializing in airport transfers.'
        },
        {
            'name': 'Fatima Sheikh',
            'phone': '+92-302-9876543',
            'email': '<EMAIL>',
            'car_plate': 'GJT-789',
            'car_code': 'CAR003',
            'car_model': 'Suzuki Cultus',
            'car_color': 'Red',
            'license_number': 'DL456789123',
            'notes': 'Female driver available for female passengers.'
        },
        {
            'name': 'Usman Ahmed',
            'phone': '+92-303-5555555',
            'email': '<EMAIL>',
            'car_plate': 'GJT-321',
            'car_code': 'CAR004',
            'car_model': 'Toyota Vitz',
            'car_color': 'Silver',
            'license_number': 'DL789123456',
            'notes': 'Young and energetic driver with excellent customer service.'
        },
        {
            'name': 'Rashid Khan',
            'phone': '+92-304-1111111',
            'email': '<EMAIL>',
            'car_plate': 'GJT-654',
            'car_code': 'CAR005',
            'car_model': 'Honda City',
            'car_color': 'Black',
            'license_number': 'DL321654987',
            'notes': 'Professional driver with luxury vehicle experience.'
        }
    ]
    
    for driver_data in drivers_data:
        driver, created = Driver.objects.get_or_create(
            car_plate=driver_data['car_plate'],
            defaults={
                **driver_data,
                'event': event,
                'is_available': True
            }
        )
        if created:
            print(f"✓ Created driver: {driver.name} ({driver.car_plate})")
    
    print("\n🎉 Sample data creation completed!")
    print(f"📊 Summary:")
    print(f"   - Events: {Event.objects.count()}")
    print(f"   - Participant Types: {ParticipantType.objects.count()}")
    print(f"   - Hotels: {Hotel.objects.count()}")
    print(f"   - Hotel Rooms: {HotelRoom.objects.count()}")
    print(f"   - Drivers: {Driver.objects.count()}")
    print(f"\n🌐 You can now test the CRUD operations at:")
    print(f"   - Frontend: http://localhost:3000")
    print(f"   - Backend Admin: http://127.0.0.1:8000/admin/")
    print(f"   - API: http://127.0.0.1:8000/api/")


if __name__ == '__main__':
    create_sample_data()

#!/bin/bash

# University of Gondar Events Management System - Server Setup Script
# For fresh Ubuntu server deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}UoG Events - Server Setup${NC}"
echo -e "${BLUE}========================================${NC}"

# Update system
echo -e "${YELLOW}📦 Updating system packages...${NC}"
sudo apt update && sudo apt upgrade -y

# Install required packages
echo -e "${YELLOW}📦 Installing required packages...${NC}"
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Install Docker
echo -e "${YELLOW}🐳 Installing Docker...${NC}"
if ! command -v docker &> /dev/null; then
    # Add Docker's official GPG key
    sudo mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker
    sudo apt update
    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # Add user to docker group
    sudo usermod -aG docker $USER
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    echo -e "${GREEN}✅ Docker installed successfully${NC}"
else
    echo -e "${GREEN}✅ Docker already installed${NC}"
fi

# Install Docker Compose (standalone)
echo -e "${YELLOW}🐳 Installing Docker Compose...${NC}"
if ! command -v docker-compose &> /dev/null; then
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    echo -e "${GREEN}✅ Docker Compose installed successfully${NC}"
else
    echo -e "${GREEN}✅ Docker Compose already installed${NC}"
fi

# Install Nginx (for SSL setup)
echo -e "${YELLOW}🌐 Installing Nginx...${NC}"
sudo apt install -y nginx

# Install Certbot for SSL
echo -e "${YELLOW}🔒 Installing Certbot for SSL...${NC}"
sudo apt install -y certbot python3-certbot-nginx

# Create project directory
echo -e "${YELLOW}📁 Creating project directory...${NC}"
sudo mkdir -p /opt/uog-events
sudo chown $USER:$USER /opt/uog-events
cd /opt/uog-events

# Create logs directory
mkdir -p logs/nginx

# Create SSL directory
mkdir -p ssl

# Download environment file template
echo -e "${YELLOW}📄 Creating environment file...${NC}"
cat > .env.prod << 'EOF'
# Production Environment Variables for UoG Events Management System

# Django Settings
SECRET_KEY=your-super-secret-production-key-change-this-immediately
DEBUG=False
ALLOWED_HOSTS=event.uog.edu.et,www.event.uog.edu.et,*************

# Database Configuration
POSTGRES_DB=uog_event_prod
POSTGRES_USER=uog_events_user
POSTGRES_PASSWORD=your-secure-database-password-change-this

# Redis Configuration
REDIS_PASSWORD=your-secure-redis-password-change-this

# CORS Settings
CORS_ALLOWED_ORIGINS=https://event.uog.edu.et,https://www.event.uog.edu.et

# Frontend URLs
REACT_APP_API_BASE_URL=https://event.uog.edu.et/api
REACT_APP_BACKEND_URL=https://event.uog.edu.et

# QR Code Base URL
QR_CODE_BASE_URL=https://event.uog.edu.et

# Email Configuration (Office 365)
EMAIL_HOST=smtp.office365.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-app-password
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=UoG Events <<EMAIL>>

# Celery Configuration
CELERY_BROKER_URL=redis://:your-secure-redis-password-change-this@redis:6379/0
CELERY_RESULT_BACKEND=redis://:your-secure-redis-password-change-this@redis:6379/0

# File Upload Limits
MAX_UPLOAD_SIZE=10485760
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp

# Cache Configuration
CACHE_TTL=300

# Rate Limiting Configuration
RATELIMIT_ENABLE=True
API_RATE_LIMIT=100/h
LOGIN_RATE_LIMIT=5/m
REGISTRATION_RATE_LIMIT=3/h

# Health Check Configuration
HEALTH_CHECK_URL=https://event.uog.edu.et/admin/login/

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
EOF

# Download docker-compose file
echo -e "${YELLOW}📄 Creating Docker Compose file...${NC}"
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django Backend
  backend:
    image: tewodrosabebaw23/uog-events-backend:latest
    restart: unless-stopped
    environment:
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=${DEBUG:-False}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - DATABASE_NAME=${POSTGRES_DB}
      - DATABASE_USER=${POSTGRES_USER}
      - DATABASE_PASSWORD=${POSTGRES_PASSWORD}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      - QR_CODE_BASE_URL=${QR_CODE_BASE_URL}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - EMAIL_USE_TLS=${EMAIL_USE_TLS}
      - DEFAULT_FROM_EMAIL=${DEFAULT_FROM_EMAIL}
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@redis:6379/0
      - HEALTH_CHECK_URL=http://backend:8000/admin/login/
    volumes:
      - media_files:/app/media
      - static_files:/app/staticfiles
      - ./logs:/app/logs
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "/app/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker
  celery-worker:
    image: tewodrosabebaw23/uog-events-backend:latest
    restart: unless-stopped
    command: celery -A event_management worker --loglevel=info --concurrency=4
    environment:
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=${DEBUG:-False}
      - DATABASE_NAME=${POSTGRES_DB}
      - DATABASE_USER=${POSTGRES_USER}
      - DATABASE_PASSWORD=${POSTGRES_PASSWORD}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - media_files:/app/media
      - ./logs:/app/logs
    depends_on:
      - db
      - redis

  # Celery Beat (Scheduler)
  celery-beat:
    image: tewodrosabebaw23/uog-events-backend:latest
    restart: unless-stopped
    command: celery -A event_management beat --loglevel=info
    environment:
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=${DEBUG:-False}
      - DATABASE_NAME=${POSTGRES_DB}
      - DATABASE_USER=${POSTGRES_USER}
      - DATABASE_PASSWORD=${POSTGRES_PASSWORD}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - ./logs:/app/logs
    depends_on:
      - db
      - redis

  # React Frontend
  frontend:
    image: tewodrosabebaw23/uog-events-frontend:latest
    restart: unless-stopped
    environment:
      - REACT_APP_API_BASE_URL=${REACT_APP_API_BASE_URL}
      - REACT_APP_BACKEND_URL=${REACT_APP_BACKEND_URL}
    depends_on:
      - backend

  # Nginx Reverse Proxy
  nginx:
    image: tewodrosabebaw23/uog-events-nginx:latest
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - static_files:/app/staticfiles:ro
      - media_files:/app/media:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
      - frontend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  media_files:
  static_files:

networks:
  default:
    driver: bridge
EOF

# Set up firewall
echo -e "${YELLOW}🔥 Configuring firewall...${NC}"
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable

# Display next steps
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}✅ Server setup completed!${NC}"
echo -e "${GREEN}========================================${NC}"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo -e "${YELLOW}1. Edit the environment file:${NC}"
echo -e "   nano /opt/uog-events/.env.prod"
echo ""
echo -e "${YELLOW}2. Update the following variables:${NC}"
echo -e "   - SECRET_KEY (generate a new one)"
echo -e "   - POSTGRES_PASSWORD"
echo -e "   - REDIS_PASSWORD"
echo -e "   - EMAIL_HOST_USER and EMAIL_HOST_PASSWORD"
echo ""
echo -e "${YELLOW}3. Set up SSL certificate:${NC}"
echo -e "   sudo certbot --nginx -d event.uog.edu.et -d www.event.uog.edu.et"
echo ""
echo -e "${YELLOW}4. Deploy the application:${NC}"
echo -e "   cd /opt/uog-events"
echo -e "   docker-compose up -d"
echo ""
echo -e "${YELLOW}5. Check deployment status:${NC}"
echo -e "   docker-compose ps"
echo -e "   docker-compose logs -f"
echo ""
echo -e "${GREEN}🎉 Ready for deployment!${NC}"

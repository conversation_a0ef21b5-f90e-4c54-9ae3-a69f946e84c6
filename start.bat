@echo off
echo ========================================
echo UoG Event Management System - Quick Start
echo ========================================
echo.

cd /d "C:\Users\<USER>\Desktop\uog event"

echo [1/4] Starting Docker containers...
docker-compose up -d
echo.

echo [2/4] Waiting for services to start...
timeout /t 15 /nobreak > nul
echo.

echo [3/4] Testing backend API...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/api/events/' -UseBasicParsing -TimeoutSec 10; Write-Host 'Backend API ready: HTTP' $response.StatusCode } catch { Write-Host 'Backend starting...' }"
echo.

echo [4/4] Starting frontend...
cd frontend
start cmd /k "npm start"
echo.

echo ========================================
echo System Started Successfully!
echo ========================================
echo.
echo Access Points:
echo - Frontend: http://localhost:3001
echo - Backend:  http://localhost:8000
echo - Admin:    http://localhost:8000/admin/
echo.
echo New Features:
echo - Hotels:    http://localhost:3001/hotels
echo - Drivers:   http://localhost:3001/drivers
echo - Types:     http://localhost:3001/participant-types
echo.

echo Container Status:
docker-compose ps
echo.

echo Opening browser...
timeout /t 3 /nobreak > nul
start http://localhost:3001

echo.
echo Application is ready! Press any key to exit...
pause > nul

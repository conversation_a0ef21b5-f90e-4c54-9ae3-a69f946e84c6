from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Event, EventSchedule, EventGallery, EmailConfiguration, EmailTemplate, EmailLog


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    list_display = ['name', 'start_date', 'end_date', 'location', 'city', 'is_active']
    list_filter = ['is_active', 'start_date', 'city']
    search_fields = ['name', 'location', 'city', 'organizer_name']
    date_hierarchy = 'start_date'


@admin.register(EventSchedule)
class EventScheduleAdmin(admin.ModelAdmin):
    list_display = ['title', 'event', 'start_time', 'end_time', 'location', 'speaker']
    list_filter = ['event', 'start_time', 'is_break']
    search_fields = ['title', 'speaker', 'location']
    date_hierarchy = 'start_time'


@admin.register(EventGallery)
class EventGalleryAdmin(admin.ModelAdmin):
    list_display = ['title', 'event', 'uploaded_at', 'is_featured']
    list_filter = ['event', 'is_featured', 'uploaded_at']
    search_fields = ['title', 'description']


@admin.register(EmailConfiguration)
class EmailConfigurationAdmin(admin.ModelAdmin):
    list_display = ['name', 'email_host', 'email_host_user', 'is_active', 'created_at']
    list_filter = ['is_active', 'email_host', 'created_at']
    search_fields = ['name', 'email_host_user']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'is_active')
        }),
        ('SMTP Configuration', {
            'fields': ('email_backend', 'email_host', 'email_port', 'email_use_tls', 'email_use_ssl')
        }),
        ('Authentication', {
            'fields': ('email_host_user', 'email_host_password', 'default_from_email')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        if obj.is_active:
            # Update Django settings with the active configuration
            from django.conf import settings
            settings.EMAIL_BACKEND = obj.email_backend
            settings.EMAIL_HOST = obj.email_host
            settings.EMAIL_PORT = obj.email_port
            settings.EMAIL_USE_TLS = obj.email_use_tls
            settings.EMAIL_USE_SSL = obj.email_use_ssl
            settings.EMAIL_HOST_USER = obj.email_host_user
            settings.EMAIL_HOST_PASSWORD = obj.email_host_password
            settings.DEFAULT_FROM_EMAIL = obj.default_from_email


@admin.register(EmailTemplate)
class EmailTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'template_type', 'subject', 'is_active', 'created_at']
    list_filter = ['template_type', 'is_active', 'created_at']
    search_fields = ['name', 'subject', 'html_content']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'template_type', 'is_active')
        }),
        ('Email Content', {
            'fields': ('subject', 'html_content', 'text_content')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        # Add help text for template variables
        form.base_fields['html_content'].help_text = mark_safe(
            "Available variables: {participant_name}, {event_name}, {event_date}, "
            "{event_location}, {site_name}, {current_year}, etc."
        )
        return form


@admin.register(EmailLog)
class EmailLogAdmin(admin.ModelAdmin):
    list_display = ['recipient_email', 'subject', 'template_type', 'status', 'sent_at', 'created_at']
    list_filter = ['status', 'template_type', 'sent_at', 'created_at']
    search_fields = ['recipient_email', 'recipient_name', 'subject']
    readonly_fields = ['recipient_email', 'recipient_name', 'subject', 'template_type',
                      'status', 'error_message', 'sent_at', 'created_at']
    date_hierarchy = 'created_at'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def get_queryset(self, request):
        return super().get_queryset(request).select_related()

# Production Environment Variables for event.uog.edu.et

# Django Settings
SECRET_KEY=your-super-secret-production-key-change-this-immediately
DEBUG=False
ALLOWED_HOSTS=event.uog.edu.et,www.event.uog.edu.et,localhost,127.0.0.1

# Database Configuration
POSTGRES_DB=uog_event_management
POSTGRES_USER=uog_event_user
POSTGRES_PASSWORD=your-secure-database-password-change-this

# Redis Configuration
REDIS_PASSWORD=your-secure-redis-password-change-this

# CORS Settings
CORS_ALLOWED_ORIGINS=https://event.uog.edu.et,https://www.event.uog.edu.et

# Frontend URLs
REACT_APP_API_BASE_URL=https://event.uog.edu.et/api
REACT_APP_BACKEND_URL=https://event.uog.edu.et

# QR Code Base URL
QR_CODE_BASE_URL=https://event.uog.edu.et

# Email Configuration (Office 365)
EMAIL_HOST=smtp.office365.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password-change-this
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=University of Gondar Events <<EMAIL>>

# Celery Configuration
CELERY_BROKER_URL=redis://:your-secure-redis-password-change-this@redis:6379/0
CELERY_RESULT_BACKEND=redis://:your-secure-redis-password-change-this@redis:6379/0

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/private.key

# Backup Configuration
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *

# Monitoring
SENTRY_DSN=your-sentry-dsn-for-error-tracking

# File Upload Limits
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp

# Security Headers
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY

# Session Security
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
CSRF_COOKIE_HTTPONLY=True

# Cache Configuration
CACHE_TTL=300  # 5 minutes default cache TTL

# Rate Limiting Configuration
RATELIMIT_ENABLE=True
API_RATE_LIMIT=100/h
LOGIN_RATE_LIMIT=5/m
REGISTRATION_RATE_LIMIT=3/h

# Health Check Configuration
HEALTH_CHECK_URL=https://event.uog.edu.et/admin/login/

# Admin URL (for security)
ADMIN_URL_PREFIX=admin

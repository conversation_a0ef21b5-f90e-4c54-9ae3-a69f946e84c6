# Security Configuration Guide
## University of Gondar Events Management System

This document outlines the security measures implemented in the UoG Events Management System and provides guidance for secure deployment.

## 🔒 Security Features Implemented

### 1. Rate Limiting
- **API Rate Limiting**: 100 requests per hour per IP address
- **Login Rate Limiting**: 5 login attempts per minute per IP address
- **Registration Rate Limiting**: 3 registrations per hour per IP address
- **Email Rate Limiting**: 10 emails per hour per IP address
- **File Upload Rate Limiting**: 20 uploads per hour per IP address

### 2. Security Headers
- **HSTS (HTTP Strict Transport Security)**: Forces HTTPS connections
- **X-Frame-Options**: Prevents clickjacking attacks
- **X-Content-Type-Options**: Prevents MIME type sniffing
- **X-XSS-Protection**: Enables browser XSS filtering
- **Referrer-Policy**: Controls referrer information
- **Content Security Policy**: Prevents XSS and data injection attacks

### 3. Session Security
- **Secure Cookies**: Cookies only sent over HTTPS in production
- **HttpOnly Cookies**: Prevents JavaScript access to session cookies
- **SameSite Cookies**: Prevents CSRF attacks
- **Session Timeout**: 30-minute session timeout
- **Session Expiry**: Sessions expire when browser closes

### 4. File Upload Security
- **File Size Limits**: Maximum 10MB per file
- **File Type Validation**: Only allowed image types (jpg, jpeg, png, gif, webp)
- **Executable File Blocking**: Prevents upload of executable files
- **File Permission Control**: Uploaded files have restricted permissions

### 5. Database Security
- **Environment Variables**: Database credentials stored in environment variables
- **Connection Encryption**: PostgreSQL connections use SSL in production
- **User Permissions**: Database user has minimal required permissions

### 6. Password Security
- **Minimum Length**: 8 characters minimum
- **Complexity Requirements**: Must not be common passwords or similar to user attributes
- **JWT Token Security**: Short-lived access tokens (1 hour) with refresh tokens (7 days)

## 🛡️ Production Security Checklist

### Before Deployment
- [ ] Change all default passwords in `.env.prod`
- [ ] Generate a strong SECRET_KEY
- [ ] Set DEBUG=False
- [ ] Configure proper ALLOWED_HOSTS
- [ ] Set up SSL certificates
- [ ] Configure firewall rules
- [ ] Set up database backups
- [ ] Configure log monitoring

### Environment Variables to Update
```bash
# Critical - Must be changed
SECRET_KEY=your-super-secret-production-key-change-this-immediately
POSTGRES_PASSWORD=your-secure-database-password-change-this
REDIS_PASSWORD=your-secure-redis-password-change-this

# Email Configuration
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-app-password

# Domain Configuration
ALLOWED_HOSTS=event.uog.edu.et,www.event.uog.edu.et
CORS_ALLOWED_ORIGINS=https://event.uog.edu.et,https://www.event.uog.edu.et
```

## 🔧 Security Configuration Files

### 1. Security Settings (`backend/event_management/security_settings.py`)
Contains all security-related Django settings that are automatically applied in production when DEBUG=False.

### 2. Rate Limiting (`backend/event_management/rate_limiting.py`)
Implements custom rate limiting decorators and middleware for API endpoints.

### 3. Nginx Security (`nginx/nginx.conf` & `nginx/conf.d/default.conf`)
- Rate limiting at the web server level
- Security headers
- SSL/TLS configuration
- Static file security

## 🚨 Security Monitoring

### Log Files
- **Security Logs**: `/app/logs/security.log`
- **Application Logs**: `/app/logs/django.log`
- **Nginx Logs**: `/var/log/nginx/access.log` and `/var/log/nginx/error.log`

### Health Checks
- Database connectivity
- Redis cache connectivity
- Web server responsiveness
- SSL certificate validity

## 🔍 Security Testing

### Rate Limiting Test
```bash
# Test API rate limiting
for i in {1..105}; do
  curl -X GET https://event.uog.edu.et/api/events/
  echo "Request $i"
done
```

### Security Headers Test
```bash
# Check security headers
curl -I https://event.uog.edu.et/
```

### SSL Test
```bash
# Test SSL configuration
openssl s_client -connect event.uog.edu.et:443 -servername event.uog.edu.et
```

## 🛠️ Incident Response

### Rate Limit Exceeded
1. Check logs for suspicious activity
2. Identify source IP addresses
3. Consider IP blocking if necessary
4. Monitor for continued attacks

### Security Alert Response
1. Check security logs immediately
2. Identify the nature of the security event
3. Take appropriate containment measures
4. Document the incident
5. Review and update security measures

## 📋 Regular Security Maintenance

### Weekly
- [ ] Review security logs
- [ ] Check for failed login attempts
- [ ] Monitor rate limiting effectiveness

### Monthly
- [ ] Update dependencies
- [ ] Review user permissions
- [ ] Test backup and recovery procedures
- [ ] Review SSL certificate expiry

### Quarterly
- [ ] Security audit
- [ ] Penetration testing
- [ ] Review and update security policies
- [ ] Update security documentation

## 🔗 Security Resources

### Django Security
- [Django Security Documentation](https://docs.djangoproject.com/en/4.2/topics/security/)
- [Django Security Checklist](https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/)

### OWASP Guidelines
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [OWASP Web Application Security Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)

### SSL/TLS
- [SSL Labs Server Test](https://www.ssllabs.com/ssltest/)
- [Mozilla SSL Configuration Generator](https://ssl-config.mozilla.org/)

## 📞 Security Contact

For security-related issues or vulnerabilities, please contact:
- **IT Security Team**: <EMAIL>
- **System Administrator**: <EMAIL>
- **Emergency Contact**: +251-xxx-xxx-xxxx

---

**Note**: This security configuration is designed for the University of Gondar Events Management System. Regular security audits and updates are recommended to maintain the highest level of security.

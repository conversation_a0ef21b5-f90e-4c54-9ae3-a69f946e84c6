[supervisord]
nodaemon=true
user=appuser
logfile=/app/logs/supervisord.log
pidfile=/app/logs/supervisord.pid

[program:gunicorn]
command=gunicorn --bind 0.0.0.0:8000 --workers 4 --worker-class gevent --worker-connections 1000 --max-requests 1000 --max-requests-jitter 100 --timeout 30 --keep-alive 2 --access-logfile /app/logs/gunicorn-access.log --error-logfile /app/logs/gunicorn-error.log --log-level info event_management.wsgi:application
directory=/app
user=appuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/gunicorn.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10

[program:celery-worker]
command=celery -A event_management worker --loglevel=info --concurrency=2
directory=/app
user=appuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/celery-worker.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stopwaitsecs=600
killasgroup=true
priority=998

[program:celery-beat]
command=celery -A event_management beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
directory=/app
user=appuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/celery-beat.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stopwaitsecs=600
killasgroup=true
priority=999

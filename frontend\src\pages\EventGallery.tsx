import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, Button, Spinner, Alert } from 'react-bootstrap';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { eventService, Event, EventGallery as EventGalleryType } from '../services/api';
import DailyGallery from '../components/DailyGallery';

const EventGallery: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [event, setEvent] = useState<Event | null>(null);
  const [gallery, setGallery] = useState<EventGalleryType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');


  useEffect(() => {
    if (id) {
      fetchEventData();
    }
  }, [id]);

  const fetchEventData = async () => {
    try {
      const [eventResponse, galleryResponse] = await Promise.all([
        eventService.getEvent(parseInt(id!)),
        eventService.getEventGallery(parseInt(id!)),
      ]);

      setEvent(eventResponse.data);
      setGallery(galleryResponse.data);
    } catch (error) {
      console.error('Error fetching event data:', error);
      setError('Failed to load event gallery');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadAll = () => {
    gallery.forEach((image, index) => {
      setTimeout(() => {
        const link = document.createElement('a');
        link.href = image.image;
        link.download = `${event?.name || 'event'}_${image.title.replace(/\s+/g, '_')}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }, index * 500); // Stagger downloads to avoid overwhelming the browser
    });
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading event gallery...</p>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-4">
        <Alert variant="danger">
          <i className="fas fa-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      </Container>
    );
  }

  if (!event) {
    return (
      <Container className="py-4">
        <Alert variant="warning">
          <i className="fas fa-question-circle me-2"></i>
          Event not found
        </Alert>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      {/* Event Header */}
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-start">
            <div>
              <h1 className="display-5 fw-bold mb-2">{event.name} - Gallery</h1>
              <p className="lead text-muted mb-3">
                Browse and download photos from the event
              </p>
              <div className="d-flex flex-wrap gap-3 mb-3">
                <div>
                  <i className="fas fa-images text-primary me-2"></i>
                  <strong>{gallery.length} photos</strong>
                </div>
                <div>
                  <i className="fas fa-calendar text-primary me-2"></i>
                  <strong>
                    {new Date(event.start_date).toLocaleDateString()} - {' '}
                    {new Date(event.end_date).toLocaleDateString()}
                  </strong>
                </div>
              </div>
            </div>
            <div>
              <Link to={`/events/${event.id}`} className="btn btn-outline-primary me-2">
                <i className="fas fa-arrow-left me-2"></i>
                Back to Event
              </Link>
              <Link to={`/events/${event.id}/schedule`} className="btn btn-outline-primary me-2">
                <i className="fas fa-clock me-2"></i>
                Schedule
              </Link>
              {gallery.length > 0 && (
                <Button variant="success" onClick={handleDownloadAll}>
                  <i className="fas fa-download me-2"></i>
                  Download All
                </Button>
              )}
            </div>
          </div>
        </Col>
      </Row>

      {/* Daily Gallery */}
      <DailyGallery galleries={gallery} eventName={event?.name || 'Event'} />




    </Container>
  );
};

export default EventGallery;

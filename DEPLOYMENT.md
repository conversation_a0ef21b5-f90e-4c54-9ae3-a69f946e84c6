# University of Gondar Event Management System - Deployment Guide

## Overview

This guide covers the deployment of the University of Gondar Event Management System using Docker containers for both local development and production environments.

## Architecture

The system consists of the following services:
- **Frontend**: React application served by Nginx
- **Backend**: Django REST API with Gunicorn
- **Database**: PostgreSQL 15
- **Cache/Queue**: Redis 7
- **Reverse Proxy**: Nginx (production only)
- **Workers**: Celery workers and beat scheduler

## Prerequisites

### Local Development
- Docker Desktop
- Docker Compose
- Git

### Production
- Ubuntu 20.04+ or CentOS 8+ server
- Docker Engine
- Docker Compose
- Domain name pointing to your server
- SSL certificates (recommended)

## Local Development Deployment

### 1. Clone the Repository
```bash
git clone <repository-url>
cd uog-event
```

### 2. Configure Environment
```bash
cp .env.local.template .env.local
# Edit .env.local with your local configuration
```

### 3. Deploy
```bash
chmod +x deploy-local.sh
./deploy-local.sh
```

### 4. Access the Application
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000/api
- Admin Panel: http://localhost:8000/admin
- Default admin credentials: admin/admin123

## Production Deployment

### 1. Server Preparation

#### Install Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### Configure Firewall
```bash
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 2. SSL Certificates

#### Option A: Let's Encrypt (Recommended)
```bash
sudo apt install certbot
sudo certbot certonly --standalone -d event.uog.edu.et
sudo cp /etc/letsencrypt/live/event.uog.edu.et/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/event.uog.edu.et/privkey.pem nginx/ssl/private.key
```

#### Option B: Custom Certificates
Place your SSL certificates in:
- `nginx/ssl/cert.pem` (certificate)
- `nginx/ssl/private.key` (private key)

### 3. Configure Environment
```bash
cp .env.prod.template .env.prod
# Edit .env.prod with your production configuration
```

**Important Environment Variables:**
```bash
SECRET_KEY=your-super-secret-production-key
POSTGRES_PASSWORD=your-secure-database-password
REDIS_PASSWORD=your-secure-redis-password
EMAIL_HOST_PASSWORD=your-email-password
ALLOWED_HOSTS=event.uog.edu.et,www.event.uog.edu.et
```

### 4. Deploy
```bash
chmod +x deploy-prod.sh
sudo ./deploy-prod.sh
```

### 5. Post-Deployment Configuration

#### Change Admin Password
```bash
docker-compose -f docker-compose.prod.yml exec backend python manage.py changepassword admin
```

#### Create Organization Data
```bash
docker-compose -f docker-compose.prod.yml exec backend python manage.py shell
```
```python
from organizations.models import Organization
org = Organization.objects.create(
    name="University of Gondar",
    motto="Devoted for Excellence",
    primary=True,
    # Add other fields as needed
)
```

## Environment Variables Reference

### Required Variables
| Variable | Description | Example |
|----------|-------------|---------|
| `SECRET_KEY` | Django secret key | `your-secret-key` |
| `POSTGRES_PASSWORD` | Database password | `secure-password` |
| `REDIS_PASSWORD` | Redis password | `redis-password` |
| `EMAIL_HOST_PASSWORD` | Email password | `email-password` |

### Optional Variables
| Variable | Default | Description |
|----------|---------|-------------|
| `DEBUG` | `False` | Django debug mode |
| `ALLOWED_HOSTS` | `localhost` | Allowed hostnames |
| `EMAIL_HOST` | `smtp.office365.com` | SMTP server |
| `EMAIL_PORT` | `587` | SMTP port |

## Docker Images

### Building Images

#### Local Development
```bash
docker-compose -f docker-compose.dev.yml build
```

#### Production
```bash
docker-compose -f docker-compose.prod.yml build
```

### Pushing to Registry (Optional)
```bash
# Tag images
docker tag uog-event_backend:latest your-registry/uog-event-backend:latest
docker tag uog-event_frontend:latest your-registry/uog-event-frontend:latest

# Push images
docker push your-registry/uog-event-backend:latest
docker push your-registry/uog-event-frontend:latest
```

## Monitoring and Maintenance

### View Logs
```bash
# All services
docker-compose -f docker-compose.prod.yml logs -f

# Specific service
docker-compose -f docker-compose.prod.yml logs -f backend
```

### Database Backup
```bash
# Manual backup
docker-compose -f docker-compose.prod.yml exec db pg_dump -U $POSTGRES_USER $POSTGRES_DB > backup.sql

# Restore backup
docker-compose -f docker-compose.prod.yml exec -T db psql -U $POSTGRES_USER $POSTGRES_DB < backup.sql
```

### Health Checks
```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# Check service health
docker-compose -f docker-compose.prod.yml exec backend python healthcheck.py
```

### Updates
```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
```

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check database logs
docker-compose -f docker-compose.prod.yml logs db

# Connect to database
docker-compose -f docker-compose.prod.yml exec db psql -U $POSTGRES_USER $POSTGRES_DB
```

#### Email Issues
```bash
# Test email configuration
docker-compose -f docker-compose.prod.yml exec backend python manage.py shell
```
```python
from django.core.mail import send_mail
send_mail('Test', 'Test message', '<EMAIL>', ['<EMAIL>'])
```

#### SSL Issues
- Verify certificate files exist and have correct permissions
- Check nginx logs for SSL-related errors
- Ensure domain DNS is properly configured

### Performance Optimization

#### Database
- Regular VACUUM and ANALYZE
- Monitor query performance
- Consider connection pooling

#### Redis
- Monitor memory usage
- Configure appropriate eviction policies
- Use Redis clustering for high availability

#### Nginx
- Enable gzip compression
- Configure proper caching headers
- Use HTTP/2

## Security Considerations

1. **Change default passwords**
2. **Use strong SECRET_KEY**
3. **Enable SSL/TLS**
4. **Configure firewall**
5. **Regular security updates**
6. **Monitor access logs**
7. **Use environment variables for secrets**

## Support

For issues and support:
- Check logs first
- Review this documentation
- Contact system administrators

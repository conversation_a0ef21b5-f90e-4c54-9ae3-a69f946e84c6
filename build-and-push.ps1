# Build and Push Docker Images to Docker Hub
# University of Gondar Events Management System
# PowerShell Version

param(
    [string]$DockerHubUsername = "uogevents",
    [string]$ImageTag = "latest",
    [string]$Version = "1.0.0"
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# Image names
$BackendImage = "$DockerHubUsername/uog-events-backend"
$FrontendImage = "$DockerHubUsername/uog-events-frontend"
$NginxImage = "$DockerHubUsername/uog-events-nginx"

Write-Host "========================================" -ForegroundColor $Blue
Write-Host "UoG Events - Docker Build & Push" -ForegroundColor $Blue
Write-Host "========================================" -ForegroundColor $Blue
Write-Host "Docker Hub Username: $DockerHubUsername" -ForegroundColor $Yellow
Write-Host "Image Tag: $ImageTag" -ForegroundColor $Yellow
Write-Host "Version: $Version" -ForegroundColor $Yellow
Write-Host ""

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor $Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker and try again." -ForegroundColor $Red
    exit 1
}

# Login to Docker Hub
Write-Host "🔐 Logging in to Docker Hub..." -ForegroundColor $Blue
try {
    docker login
    if ($LASTEXITCODE -ne 0) {
        throw "Docker login failed"
    }
    Write-Host "✅ Successfully logged in to Docker Hub" -ForegroundColor $Green
} catch {
    Write-Host "❌ Failed to login to Docker Hub" -ForegroundColor $Red
    exit 1
}

# Build Backend Image
Write-Host "🏗️  Building Backend Image..." -ForegroundColor $Blue
try {
    docker build -t "${BackendImage}:${ImageTag}" -t "${BackendImage}:${Version}" -f backend/Dockerfile.prod backend/
    if ($LASTEXITCODE -ne 0) {
        throw "Backend build failed"
    }
    Write-Host "✅ Backend image built successfully" -ForegroundColor $Green
} catch {
    Write-Host "❌ Failed to build backend image" -ForegroundColor $Red
    exit 1
}

# Build Frontend Image
Write-Host "🏗️  Building Frontend Image..." -ForegroundColor $Blue
try {
    docker build -t "${FrontendImage}:${ImageTag}" -t "${FrontendImage}:${Version}" `
        --build-arg REACT_APP_API_BASE_URL=https://event.uog.edu.et/api `
        --build-arg REACT_APP_BACKEND_URL=https://event.uog.edu.et `
        -f frontend/Dockerfile.prod frontend/
    if ($LASTEXITCODE -ne 0) {
        throw "Frontend build failed"
    }
    Write-Host "✅ Frontend image built successfully" -ForegroundColor $Green
} catch {
    Write-Host "❌ Failed to build frontend image" -ForegroundColor $Red
    exit 1
}

# Build Nginx Image
Write-Host "🏗️  Building Nginx Image..." -ForegroundColor $Blue
try {
    docker build -t "${NginxImage}:${ImageTag}" -t "${NginxImage}:${Version}" nginx/
    if ($LASTEXITCODE -ne 0) {
        throw "Nginx build failed"
    }
    Write-Host "✅ Nginx image built successfully" -ForegroundColor $Green
} catch {
    Write-Host "❌ Failed to build nginx image" -ForegroundColor $Red
    exit 1
}

# Push Images to Docker Hub
Write-Host "📤 Pushing images to Docker Hub..." -ForegroundColor $Blue

Write-Host "Pushing Backend Image..." -ForegroundColor $Yellow
docker push "${BackendImage}:${ImageTag}"
docker push "${BackendImage}:${Version}"

Write-Host "Pushing Frontend Image..." -ForegroundColor $Yellow
docker push "${FrontendImage}:${ImageTag}"
docker push "${FrontendImage}:${Version}"

Write-Host "Pushing Nginx Image..." -ForegroundColor $Yellow
docker push "${NginxImage}:${ImageTag}"
docker push "${NginxImage}:${Version}"

# Display image information
Write-Host "========================================" -ForegroundColor $Green
Write-Host "✅ All images built and pushed successfully!" -ForegroundColor $Green
Write-Host "========================================" -ForegroundColor $Green
Write-Host ""
Write-Host "📋 Image Information:" -ForegroundColor $Blue
Write-Host "Backend:  ${BackendImage}:${ImageTag}" -ForegroundColor $Yellow
Write-Host "Frontend: ${FrontendImage}:${ImageTag}" -ForegroundColor $Yellow
Write-Host "Nginx:    ${NginxImage}:${ImageTag}" -ForegroundColor $Yellow
Write-Host ""
Write-Host "📋 Versioned Images:" -ForegroundColor $Blue
Write-Host "Backend:  ${BackendImage}:${Version}" -ForegroundColor $Yellow
Write-Host "Frontend: ${FrontendImage}:${Version}" -ForegroundColor $Yellow
Write-Host "Nginx:    ${NginxImage}:${Version}" -ForegroundColor $Yellow
Write-Host ""

# Create docker-compose.hub.yml for using Docker Hub images
Write-Host "📝 Creating docker-compose.hub.yml..." -ForegroundColor $Blue

$dockerComposeContent = @"
version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: `${POSTGRES_DB}
      POSTGRES_USER: `${POSTGRES_USER}
      POSTGRES_PASSWORD: `${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U `${POSTGRES_USER} -d `${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --requirepass `${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django Backend
  backend:
    image: ${BackendImage}:${ImageTag}
    restart: unless-stopped
    environment:
      - SECRET_KEY=`${SECRET_KEY}
      - DEBUG=`${DEBUG:-False}
      - ALLOWED_HOSTS=`${ALLOWED_HOSTS}
      - DATABASE_NAME=`${POSTGRES_DB}
      - DATABASE_USER=`${POSTGRES_USER}
      - DATABASE_PASSWORD=`${POSTGRES_PASSWORD}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:`${REDIS_PASSWORD}@redis:6379/0
      - CORS_ALLOWED_ORIGINS=`${CORS_ALLOWED_ORIGINS}
      - QR_CODE_BASE_URL=`${QR_CODE_BASE_URL}
      - EMAIL_HOST=`${EMAIL_HOST}
      - EMAIL_PORT=`${EMAIL_PORT}
      - EMAIL_HOST_USER=`${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=`${EMAIL_HOST_PASSWORD}
      - EMAIL_USE_TLS=`${EMAIL_USE_TLS}
      - DEFAULT_FROM_EMAIL=`${DEFAULT_FROM_EMAIL}
      - CELERY_BROKER_URL=redis://:`${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:`${REDIS_PASSWORD}@redis:6379/0
      - HEALTH_CHECK_URL=http://backend:8000/admin/login/
    volumes:
      - media_files:/app/media
      - static_files:/app/staticfiles
      - ./logs:/app/logs
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "/app/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker
  celery-worker:
    image: ${BackendImage}:${ImageTag}
    restart: unless-stopped
    command: celery -A event_management worker --loglevel=info --concurrency=4
    environment:
      - SECRET_KEY=`${SECRET_KEY}
      - DEBUG=`${DEBUG:-False}
      - DATABASE_NAME=`${POSTGRES_DB}
      - DATABASE_USER=`${POSTGRES_USER}
      - DATABASE_PASSWORD=`${POSTGRES_PASSWORD}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:`${REDIS_PASSWORD}@redis:6379/0
      - CELERY_BROKER_URL=redis://:`${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:`${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - media_files:/app/media
      - ./logs:/app/logs
    depends_on:
      - db
      - redis

  # Celery Beat (Scheduler)
  celery-beat:
    image: ${BackendImage}:${ImageTag}
    restart: unless-stopped
    command: celery -A event_management beat --loglevel=info
    environment:
      - SECRET_KEY=`${SECRET_KEY}
      - DEBUG=`${DEBUG:-False}
      - DATABASE_NAME=`${POSTGRES_DB}
      - DATABASE_USER=`${POSTGRES_USER}
      - DATABASE_PASSWORD=`${POSTGRES_PASSWORD}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:`${REDIS_PASSWORD}@redis:6379/0
      - CELERY_BROKER_URL=redis://:`${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:`${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - ./logs:/app/logs
    depends_on:
      - db
      - redis

  # React Frontend
  frontend:
    image: ${FrontendImage}:${ImageTag}
    restart: unless-stopped
    environment:
      - REACT_APP_API_BASE_URL=`${REACT_APP_API_BASE_URL}
      - REACT_APP_BACKEND_URL=`${REACT_APP_BACKEND_URL}
    depends_on:
      - backend

  # Nginx Reverse Proxy
  nginx:
    image: ${NginxImage}:${ImageTag}
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - static_files:/app/staticfiles:ro
      - media_files:/app/media:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
      - frontend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  media_files:
  static_files:

networks:
  default:
    driver: bridge
"@

$dockerComposeContent | Out-File -FilePath "docker-compose.hub.yml" -Encoding UTF8

Write-Host "✅ docker-compose.hub.yml created successfully" -ForegroundColor $Green
Write-Host ""
Write-Host "🚀 To deploy using Docker Hub images:" -ForegroundColor $Blue
Write-Host "1. Copy .env.prod to your server" -ForegroundColor $Yellow
Write-Host "2. Copy docker-compose.hub.yml to your server" -ForegroundColor $Yellow
Write-Host "3. Run: docker-compose -f docker-compose.hub.yml up -d" -ForegroundColor $Yellow
Write-Host ""
Write-Host "🎉 Build and push completed successfully!" -ForegroundColor $Green

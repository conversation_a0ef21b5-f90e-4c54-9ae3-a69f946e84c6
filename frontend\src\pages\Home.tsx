import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { developerService, Developer, eventService, Event } from '../services/api';
import LandingNavbar from '../components/LandingNavbar';
import { useBranding } from '../hooks/useBranding';
import '../styles/landing.css';

const Home: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const { organization } = useBranding();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [developers, setDevelopers] = useState<Developer[]>([]);
  const [developersLoading, setDevelopersLoading] = useState(true);
  const [realEvents, setRealEvents] = useState<Event[]>([]);
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  const heroSlides = [
    {
      title: "International Conference 2024",
      subtitle: "Innovation in Higher Education",
      description: "Join us for a groundbreaking conference on educational excellence and research innovation at the University of Gondar",
      background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      date: "March 15-17, 2024",
      location: "University of Gondar Main Campus",
      eventDate: new Date('2024-03-15T09:00:00'),
      participants: 500,
      universities: 56,
      countries: 12,
      speakers: 25
    },
    {
      title: "Academic Excellence Summit",
      subtitle: "Shaping Tomorrow's Leaders",
      description: "Discover cutting-edge research, network with global academics, and explore collaborative opportunities",
      background: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      date: "April 20-22, 2024",
      location: "Gondar Conference Center",
      eventDate: new Date('2024-04-20T09:00:00'),
      participants: 350,
      universities: 42,
      countries: 8,
      speakers: 18
    },
    {
      title: "Ethiopian Heritage Symposium",
      subtitle: "Preserving Our Legacy",
      description: "Celebrating Ethiopia's rich cultural heritage while embracing modern educational methodologies",
      background: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      date: "May 10-12, 2024",
      location: "Historic Gondar Castle Grounds",
      eventDate: new Date('2024-05-10T09:00:00'),
      participants: 280,
      universities: 35,
      countries: 6,
      speakers: 15
    }
  ];

  const navigationSections = [
    { id: 'events', label: 'Current Events', icon: 'fas fa-calendar-alt' },
    { id: 'university', label: 'University of Gondar', icon: 'fas fa-university' },
    { id: 'attractions', label: 'Gondar Attractions', icon: 'fas fa-landmark' },
    { id: 'campus', label: 'Campus Visit', icon: 'fas fa-map-marked-alt' },
    { id: 'all-events', label: 'All Events', icon: 'fas fa-calendar-check' }
  ];

  // Function to convert real events to hero slide format
  const convertEventToSlide = (event: Event, index: number) => {
    const backgrounds = [
      "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      "linear-gradient(135deg, #fa709a 0%, #fee140 100%)"
    ];

    const formatEventDate = (startDate: string, endDate: string) => {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };

      if (start.toDateString() === end.toDateString()) {
        return start.toLocaleDateString('en-US', options);
      } else {
        return `${start.toLocaleDateString('en-US', options)} - ${end.toLocaleDateString('en-US', options)}`;
      }
    };

    return {
      title: event.name,
      subtitle: `${event.city}, ${event.country}`,
      description: event.description,
      background: backgrounds[index % backgrounds.length],
      date: formatEventDate(event.start_date, event.end_date),
      location: event.location,
      eventDate: new Date(event.start_date),
      participants: event.participant_count || 0,
      universities: Math.floor(event.participant_count / 10) || 5,
      countries: Math.floor(event.participant_count / 50) || 3,
      speakers: Math.floor(event.participant_count / 20) || 8
    };
  };

  // Get current slides (real events or fallback to static)
  const getCurrentSlides = () => {
    if (realEvents.length > 0) {
      return realEvents.map(convertEventToSlide);
    }
    return heroSlides;
  };

  const currentSlides = getCurrentSlides();

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % currentSlides.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [currentSlides.length]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch developers
        const developersResponse = await developerService.getDevelopers();
        setDevelopers(developersResponse.data.results || []);

        // Fetch real events using public API
        const eventsResponse = await eventService.getPublicEvents();
        const eventsData = eventsResponse.data.results || [];

        // Get current and upcoming active events
        const now = new Date();
        const currentAndUpcomingEvents = eventsData
          .filter((event: Event) => event.is_active && new Date(event.end_date) >= now)
          .sort((a: Event, b: Event) => new Date(a.start_date).getTime() - new Date(b.start_date).getTime());

        setRealEvents(currentAndUpcomingEvents);

        // Organization data is now handled by the branding hook
      } catch (error) {
        console.error('Error fetching data:', error);
        setDevelopers([]);
      }
    };
    fetchData();
  }, []);

  // Countdown timer effect
  useEffect(() => {
    const calculateTimeLeft = () => {
      if (currentSlides.length > 0 && currentSlides[currentSlide]) {
        const currentEvent = currentSlides[currentSlide];
        const difference = +currentEvent.eventDate - +new Date();

        if (difference > 0) {
          setTimeLeft({
            days: Math.floor(difference / (1000 * 60 * 60 * 24)),
            hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
            minutes: Math.floor((difference / 1000 / 60) % 60),
            seconds: Math.floor((difference / 1000) % 60)
          });
        } else {
          setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
        }
      }
    };

    // Only set up timer if we have slides
    if (currentSlides.length > 0) {
      calculateTimeLeft();
      const timer = setInterval(calculateTimeLeft, 1000);
      return () => clearInterval(timer);
    }
  }, [currentSlide, currentSlides]);

  if (isAuthenticated) {
    return (
      <div className="min-vh-100 d-flex align-items-center justify-content-center" 
           style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <div className="text-center text-white">
          <h1 className="display-3 fw-bold mb-4">Welcome Back!</h1>
          <p className="lead mb-4">Continue managing your events</p>
          <div className="d-flex gap-3 justify-content-center">
            <Link to="/dashboard" className="btn btn-light btn-lg px-4">
              <i className="fas fa-tachometer-alt me-2"></i>
              Dashboard
            </Link>
            <Link to="/events" className="btn btn-outline-light btn-lg px-4">
              <i className="fas fa-calendar me-2"></i>
              Events
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="landing-page">
      {/* Navigation Menu for Landing Page */}
      <LandingNavbar />

      {/* Hero Section with Current Events */}
      <section id="events" className="hero-section position-relative overflow-hidden" style={{ marginTop: '80px' }}>
        <div
          className="hero-background position-absolute w-100 h-100"
          style={{
            background: currentSlides[currentSlide]?.background || heroSlides[0].background,
            transition: 'all 1s ease-in-out'
          }}
        />

        {/* Animated Particles */}
        <div className="particles position-absolute w-100 h-100">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="particle position-absolute rounded-circle bg-white"
              style={{
                width: Math.random() * 4 + 2 + 'px',
                height: Math.random() * 4 + 2 + 'px',
                left: Math.random() * 100 + '%',
                top: Math.random() * 100 + '%',
                opacity: Math.random() * 0.5 + 0.1,
                animation: `float ${Math.random() * 3 + 2}s ease-in-out infinite alternate`
              }}
            />
          ))}
        </div>

        <div className="hero-content position-relative d-flex align-items-center min-vh-100">
          <div className="modern-container">
            <div className="row align-items-center">
              {/* Left Side - Event Details */}
              <div className="col-lg-6">
                <div className="hero-text text-white">
                  <h1 className="display-3 fw-bold mb-4 hero-title">
                    {currentSlides[currentSlide]?.title || heroSlides[0].title}
                  </h1>
                  <h2 className="display-6 mb-4 hero-subtitle">
                    {currentSlides[currentSlide]?.subtitle || heroSlides[0].subtitle}
                  </h2>
                  <p className="lead mb-4 hero-description" style={{ fontSize: '1.2rem' }}>
                    {currentSlides[currentSlide]?.description || heroSlides[0].description}
                  </p>

                  {/* Event Statistics */}
                  <div className="event-stats mb-4">
                    <div className="row g-3">
                      <div className="col-6">
                        <div className="stat-item text-center hero-glass-card p-3">
                          <h3 className="fw-bold text-warning mb-1 stat-number">{currentSlides[currentSlide]?.participants || heroSlides[0].participants}+</h3>
                          <p className="small mb-0 text-white stat-label">Participants</p>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="stat-item text-center hero-glass-card p-3">
                          <h3 className="fw-bold text-success mb-1 stat-number">{currentSlides[currentSlide]?.universities || heroSlides[0].universities}+</h3>
                          <p className="small mb-0 text-white stat-label">Universities</p>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="stat-item text-center hero-glass-card p-3">
                          <h3 className="fw-bold text-info mb-1 stat-number">{currentSlides[currentSlide]?.countries || heroSlides[0].countries}+</h3>
                          <p className="small mb-0 text-white stat-label">Countries</p>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="stat-item text-center hero-glass-card p-3">
                          <h3 className="fw-bold text-danger mb-1 stat-number">{currentSlides[currentSlide]?.speakers || heroSlides[0].speakers}+</h3>
                          <p className="small mb-0 text-white stat-label">Speakers</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Event Details */}
                  <div className="event-details mb-4">
                    <div className="row g-3">
                      <div className="col-12">
                        <div className="d-flex align-items-center mb-3">
                          <i className="fas fa-calendar-alt text-warning me-3 fa-lg"></i>
                          <span className="fs-6">{currentSlides[currentSlide]?.date || heroSlides[0].date}</span>
                        </div>
                      </div>
                      <div className="col-12">
                        <div className="d-flex align-items-center">
                          <i className="fas fa-map-marker-alt text-success me-3 fa-lg"></i>
                          <span className="fs-6">{currentSlides[currentSlide]?.location || heroSlides[0].location}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Side - Countdown and Actions */}
              <div className="col-lg-6">
                <div className="hero-actions-panel hero-glass-panel">
                  {/* Countdown Timer */}
                  <div className="countdown-timer hero-glass-panel rounded-4 p-4 mb-4">
                    <h4 className="text-center text-white mb-4 countdown-title">
                      <i className="fas fa-clock me-2 text-warning"></i>
                      Event Starts In
                    </h4>
                    <div className="row g-3 text-center">
                      <div className="col-3">
                        <div className="countdown-item hero-glass-card p-3">
                          <h2 className="fw-bold text-white mb-1 countdown-number">{timeLeft.days.toString().padStart(2, '0')}</h2>
                          <p className="small mb-0 text-white countdown-label">Days</p>
                        </div>
                      </div>
                      <div className="col-3">
                        <div className="countdown-item hero-glass-card p-3">
                          <h2 className="fw-bold text-white mb-1 countdown-number">{timeLeft.hours.toString().padStart(2, '0')}</h2>
                          <p className="small mb-0 text-white countdown-label">Hours</p>
                        </div>
                      </div>
                      <div className="col-3">
                        <div className="countdown-item hero-glass-card p-3">
                          <h2 className="fw-bold text-white mb-1 countdown-number">{timeLeft.minutes.toString().padStart(2, '0')}</h2>
                          <p className="small mb-0 text-white countdown-label">Minutes</p>
                        </div>
                      </div>
                      <div className="col-3">
                        <div className="countdown-item hero-glass-card p-3">
                          <h2 className="fw-bold text-white mb-1 countdown-number">{timeLeft.seconds.toString().padStart(2, '0')}</h2>
                          <p className="small mb-0 text-white countdown-label">Seconds</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="hero-actions text-center">
                    <a
                      href="/participant-register"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn btn-light btn-lg me-3 mb-3 px-5 py-3 rounded-pill shadow-lg"
                      style={{ fontSize: '1.1rem' }}
                    >
                      <i className="fas fa-ticket-alt me-2"></i>
                      Register Now
                    </a>
                    <Link
                      to="/events"
                      className="btn btn-outline-light btn-lg mb-3 px-5 py-3 rounded-pill"
                      style={{ fontSize: '1.1rem' }}
                    >
                      <i className="fas fa-info-circle me-2"></i>
                      Learn More
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            {/* Slide Indicators */}
            <div className="slide-indicators position-absolute bottom-0 start-50 translate-middle-x mb-4">
              {currentSlides.map((_, index) => (
                <button
                  key={index}
                  className={`indicator ${index === currentSlide ? 'active' : ''}`}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* University of Gondar Section */}
      <section id="university" className="university-section py-5" style={{ background: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)' }}>
        <div className="section-container">
          <div className="row text-center mb-5">
            <div className="col-lg-8 mx-auto">
              <h2 className="display-4 fw-bold mb-4 text-white">University of Gondar</h2>
              <p className="lead text-light">
                Founded in 1954, the University of Gondar stands as a beacon of academic excellence in Ethiopia's historic imperial city
              </p>
            </div>
          </div>

          <div className="row align-items-center">
            <div className="col-lg-6 mb-4">
              <div className="university-stats">
                <div className="row g-4">
                  <div className="col-6">
                    <div className="stat-card text-center p-4 bg-white rounded-4 shadow">
                      <h3 className="text-primary mb-2 counter">70+</h3>
                      <p className="text-muted mb-0">Years of Excellence</p>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="stat-card text-center p-4 bg-white rounded-4 shadow">
                      <h3 className="text-success mb-2 counter">60,000+</h3>
                      <p className="text-muted mb-0">Students</p>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="stat-card text-center p-4 bg-white rounded-4 shadow">
                      <h3 className="text-warning mb-2 counter">13</h3>
                      <p className="text-muted mb-0">Colleges & Schools</p>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="stat-card text-center p-4 bg-white rounded-4 shadow">
                      <h3 className="text-info mb-2 counter">200+</h3>
                      <p className="text-muted mb-0">Programs</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="university-highlights text-white">
                <h3 className="mb-4">Academic Excellence</h3>
                <div className="highlight-item d-flex align-items-center mb-3">
                  <i className="fas fa-graduation-cap text-warning me-3 fa-2x"></i>
                  <div>
                    <h5 className="mb-1">Quality Education</h5>
                    <p className="mb-0 text-light">Internationally recognized programs and degrees</p>
                  </div>
                </div>
                <div className="highlight-item d-flex align-items-center mb-3">
                  <i className="fas fa-microscope text-info me-3 fa-2x"></i>
                  <div>
                    <h5 className="mb-1">Research Excellence</h5>
                    <p className="mb-0 text-light">Cutting-edge research facilities and programs</p>
                  </div>
                </div>
                <div className="highlight-item d-flex align-items-center mb-3">
                  <i className="fas fa-globe-africa text-success me-3 fa-2x"></i>
                  <div>
                    <h5 className="mb-1">Global Recognition</h5>
                    <p className="mb-0 text-light">International partnerships and collaborations</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gondar Attractions Section */}
      <section id="attractions" className="attractions-section py-5" style={{ background: '#f8f9fa' }}>
        <div className="section-container">
          <div className="row text-center mb-5">
            <div className="col-lg-8 mx-auto">
              <h2 className="display-4 fw-bold mb-4">Discover Historic Gondar</h2>
              <p className="lead text-muted">
                Explore Ethiopia's ancient imperial city, known as the "Camelot of Africa"
              </p>
            </div>
          </div>

          <div className="row g-4">
            {[
              {
                title: 'Royal Fasil Ghebbi',
                description: 'UNESCO World Heritage Site featuring 17th-century castles and palaces',
                icon: 'fas fa-crown',
                color: '#e74c3c'
              },
              {
                title: 'Debre Berhan Selassie',
                description: 'Famous church with stunning ceiling paintings of angelic faces',
                icon: 'fas fa-church',
                color: '#3498db'
              },
              {
                title: 'Fasilides Bath',
                description: 'Historic bathing complex used for Timkat celebrations',
                icon: 'fas fa-water',
                color: '#2ecc71'
              },
              {
                title: 'Simien Mountains',
                description: 'Nearby national park with breathtaking landscapes and wildlife',
                icon: 'fas fa-mountain',
                color: '#f39c12'
              }
            ].map((attraction, index) => (
              <div key={index} className="col-lg-3 col-md-6">
                <div className="attraction-card h-100 p-4 bg-white rounded-4 shadow-sm border-0 text-center">
                  <div
                    className="attraction-icon mb-3 mx-auto rounded-circle d-flex align-items-center justify-content-center"
                    style={{
                      width: '80px',
                      height: '80px',
                      background: `linear-gradient(135deg, ${attraction.color}, ${attraction.color}dd)`
                    }}
                  >
                    <i className={`${attraction.icon} fa-2x text-white`}></i>
                  </div>
                  <h5 className="fw-bold mb-3">{attraction.title}</h5>
                  <p className="text-muted">{attraction.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Campus Life Section */}
      <section id="campus" className="campus-life-section position-relative overflow-hidden py-0">
        {/* Campus Background */}
        <div
          className="campus-background position-absolute w-100 h-100"
          style={{
            background: 'linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url("https://images.unsplash.com/photo-1562774053-701939374585?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80")',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundAttachment: 'fixed'
          }}
        />

        <div className="section-container position-relative" style={{ zIndex: 2 }}>
          <div className="row align-items-center min-vh-100">
            {/* Left Card - Campus Activities */}
            <div className="col-lg-4">
              <div className="campus-card bg-white rounded-4 shadow-lg p-4 mb-4">
                <div className="campus-card-header mb-3">
                  <span className="badge bg-primary text-white px-3 py-2 rounded-pill mb-3">
                    EXPLORE LIFE AT UOG
                  </span>
                  <div className="campus-card-image mb-3">
                    <img
                      src="https://images.unsplash.com/photo-1523050854058-8df90110c9d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                      alt="Campus Activities"
                      className="w-100 rounded-3"
                      style={{ height: '200px', objectFit: 'cover' }}
                    />
                  </div>
                  <div className="campus-label">
                    <span className="text-primary small fw-bold">Every Campus</span>
                    <h4 className="fw-bold mb-3">Events and Activities</h4>
                  </div>
                </div>
                <p className="text-muted mb-3">
                  Our students engage in events, activities, and clubs year-round across all campuses. From academic conferences to cultural celebrations and community involvement, we offer plenty of opportunities to socialize.
                </p>
                <a href="#all-events" className="btn btn-link p-0 text-primary fw-bold text-decoration-none">
                  View Gallery <i className="fas fa-arrow-right ms-2"></i>
                </a>
              </div>
            </div>

            {/* Center - Campus Life Title */}
            <div className="col-lg-4 text-center">
              <div className="campus-life-title">
                <h1 className="display-1 fw-bold text-white campus-life-text">
                  CAMPUS LIFE
                </h1>
              </div>
            </div>

            {/* Right Card - Sports Festival */}
            <div className="col-lg-4">
              <div className="campus-card bg-white rounded-4 shadow-lg p-4 mb-4">
                <div className="campus-card-header mb-3">
                  <span className="badge bg-danger text-white px-3 py-2 rounded-pill mb-3">
                    UOG SPORT FESTIVAL
                  </span>
                  <div className="campus-card-image mb-3">
                    <img
                      src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                      alt="Sports Festival"
                      className="w-100 rounded-3"
                      style={{ height: '200px', objectFit: 'cover' }}
                    />
                  </div>
                  <div className="campus-label">
                    <span className="text-danger small fw-bold">Great Info Campus</span>
                    <h4 className="fw-bold mb-3">Annual Sports Festival</h4>
                  </div>
                </div>
                <p className="text-muted mb-3">
                  Experience the spirit of competition and community at UoG's Annual Sports Festival, celebrating excellence in student athletics and intercollegiate unity.
                </p>
                <a href="#all-events" className="btn btn-link p-0 text-primary fw-bold text-decoration-none">
                  View Gallery <i className="fas fa-arrow-right ms-2"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Arrows */}
        <button className="campus-nav-btn campus-nav-prev position-absolute top-50 start-0 translate-middle-y ms-3">
          <i className="fas fa-chevron-left"></i>
        </button>
        <button className="campus-nav-btn campus-nav-next position-absolute top-50 end-0 translate-middle-y me-3">
          <i className="fas fa-chevron-right"></i>
        </button>
      </section>

      {/* All Events Section */}
      <section id="all-events" className="events-section py-5" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <div className="section-container">
          <div className="row text-center mb-5">
            <div className="col-lg-8 mx-auto">
              <h2 className="display-4 fw-bold mb-4 text-white">University Events</h2>
              <p className="lead text-light">
                Discover our comprehensive calendar of academic, cultural, and research events happening at the University of Gondar
              </p>
            </div>
          </div>

          <div className="row align-items-center">
            <div className="col-lg-6 mb-4">
              <div className="event-stats">
                <div className="row g-4">
                  <div className="col-6">
                    <div className="stat-card text-center p-4 bg-white rounded-4 shadow">
                      <h3 className="text-primary mb-2 counter">{realEvents.length || 12}+</h3>
                      <p className="text-muted mb-0">Active Events</p>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="stat-card text-center p-4 bg-white rounded-4 shadow">
                      <h3 className="text-success mb-2 counter">500+</h3>
                      <p className="text-muted mb-0">Participants</p>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="stat-card text-center p-4 bg-white rounded-4 shadow">
                      <h3 className="text-warning mb-2 counter">25+</h3>
                      <p className="text-muted mb-0">International Speakers</p>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="stat-card text-center p-4 bg-white rounded-4 shadow">
                      <h3 className="text-info mb-2 counter">15+</h3>
                      <p className="text-muted mb-0">Countries</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="event-highlights text-white">
                <div className="highlight-item d-flex align-items-center mb-3">
                  <i className="fas fa-calendar-check text-success me-3 fa-2x"></i>
                  <div>
                    <h5 className="mb-1">International Conferences</h5>
                    <p className="mb-0 text-light">World-class academic conferences and symposiums</p>
                  </div>
                </div>
                <div className="highlight-item d-flex align-items-center mb-3">
                  <i className="fas fa-users text-warning me-3 fa-2x"></i>
                  <div>
                    <h5 className="mb-1">Networking Opportunities</h5>
                    <p className="mb-0 text-light">Connect with scholars and professionals globally</p>
                  </div>
                </div>
                <div className="highlight-item d-flex align-items-center mb-3">
                  <i className="fas fa-certificate text-info me-3 fa-2x"></i>
                  <div>
                    <h5 className="mb-1">Professional Development</h5>
                    <p className="mb-0 text-light">Workshops and training sessions for career growth</p>
                  </div>
                </div>
                <div className="highlight-item d-flex align-items-center mb-3">
                  <i className="fas fa-globe text-primary me-3 fa-2x"></i>
                  <div>
                    <h5 className="mb-1">Global Collaboration</h5>
                    <p className="mb-0 text-light">International partnerships and research collaborations</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* University Systems Section */}
      <section className="systems-section py-5" style={{ background: '#f8f9fa' }}>
        <div className="section-container">
          <div className="row text-center mb-5">
            <div className="col-lg-8 mx-auto">
              <h2 className="display-4 fw-bold mb-4">University Digital Systems</h2>
              <p className="lead text-muted">
                Explore our comprehensive digital ecosystem designed to serve students, faculty, and alumni
              </p>
            </div>
          </div>

          <div className="row g-4">
            {[
              {
                title: "Alumni Portal",
                description: "Connect with fellow graduates and access alumni services",
                url: "https://alumni.uog.edu.et/",
                icon: "fas fa-graduation-cap",
                color: "#667eea"
              },
              {
                title: "Who's Who Directory",
                description: "Discover notable personalities from University of Gondar",
                url: "https://whoswho.uog.edu.et/",
                icon: "fas fa-users",
                color: "#f093fb"
              },
              {
                title: "Student Portal",
                description: "Access academic records, courses, and student services",
                url: "https://portal.uog.edu.et/",
                icon: "fas fa-user-graduate",
                color: "#4facfe"
              },
              {
                title: "Graduate Verification",
                description: "Verify graduate credentials and academic achievements",
                url: "https://portal.uog.edu.et/graduate-verification",
                icon: "fas fa-certificate",
                color: "#43e97b"
              },
              {
                title: "Registrar Services",
                description: "Access official academic and administrative services",
                url: "https://portal.uog.edu.et/services",
                icon: "fas fa-file-alt",
                color: "#fa709a"
              },
              {
                title: "Transcript Tracking",
                description: "Track your official transcript requests and delivery",
                url: "https://portal.uog.edu.et/track-official",
                icon: "fas fa-search",
                color: "#ffecd2"
              }
            ].map((system, index) => (
              <div key={index} className="col-lg-4 col-md-6">
                <div className="system-card h-100 p-4 bg-white rounded-4 shadow-sm border-0 text-center">
                  <div
                    className="system-icon mb-3 mx-auto rounded-circle d-flex align-items-center justify-content-center"
                    style={{
                      width: '80px',
                      height: '80px',
                      background: `linear-gradient(135deg, ${system.color}, ${system.color}dd)`
                    }}
                  >
                    <i className={`${system.icon} fa-2x text-white`}></i>
                  </div>
                  <h5 className="fw-bold mb-3">{system.title}</h5>
                  <p className="text-muted mb-4">{system.description}</p>
                  <a
                    href={system.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn btn-outline-primary btn-sm px-4"
                  >
                    <i className="fas fa-external-link-alt me-2"></i>
                    Access System
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Registration Section */}
      <section id="register" className="registration-section py-5" style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        minHeight: '100vh'
      }}>
        <div className="section-container">
          <div className="row text-center mb-5">
            <div className="col-lg-8 mx-auto">
              <h2 className="display-4 fw-bold mb-4 text-white">Event Registration</h2>
              <p className="lead text-light">
                Join us for an amazing experience at the University of Gondar. Register now to secure your spot at our upcoming events!
              </p>
            </div>
          </div>

          <div className="row justify-content-center">
            <div className="col-lg-10">
              <div className="text-center mb-4">
                <a
                  href="/participant-register"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-light btn-lg px-5 py-3 rounded-pill shadow-lg"
                  style={{ fontSize: '1.2rem' }}
                >
                  <i className="fas fa-user-plus me-3"></i>
                  Start Registration Process
                </a>
              </div>

              {/* Registration Stats */}
              <div className="row g-4">
                <div className="col-6 col-md-3">
                  <div className="hero-glass-card p-4 text-center">
                    <h3 className="fw-bold text-warning mb-1">500+</h3>
                    <small className="text-white">Registered Participants</small>
                  </div>
                </div>
                <div className="col-6 col-md-3">
                  <div className="hero-glass-card p-4 text-center">
                    <h3 className="fw-bold text-success mb-1">50+</h3>
                    <small className="text-white">Universities</small>
                  </div>
                </div>
                <div className="col-6 col-md-3">
                  <div className="hero-glass-card p-4 text-center">
                    <h3 className="fw-bold text-info mb-1">15+</h3>
                    <small className="text-white">Countries</small>
                  </div>
                </div>
                <div className="col-6 col-md-3">
                  <div className="hero-glass-card p-4 text-center">
                    <h3 className="fw-bold text-danger mb-1">25+</h3>
                    <small className="text-white">International Speakers</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer-section">
        <div className="footer-content py-5">
          <div className="section-container">
            <div className="row g-4">
              <div className="col-lg-4">
                <div className="footer-brand mb-4">
                  <div className="organization-logo mb-4 text-center">
                    {organization?.logo ? (
                      <img
                        src={organization.logo}
                        alt={organization.name}
                        className="footer-logo mb-3"
                        style={{
                          maxWidth: '120px',
                          height: 'auto',
                          filter: 'brightness(0) invert(1)', // Makes logo white
                          opacity: 0.9
                        }}
                      />
                    ) : (
                      <i className="fas fa-university text-primary mb-3" style={{ fontSize: '3rem' }}></i>
                    )}
                    <h4 className="fw-bold text-white">
                      {organization?.name || 'University of Gondar'}
                    </h4>
                    <p className="text-light mb-0" style={{ fontSize: '0.9rem' }}>
                      {organization?.motto || 'Excellence in Education, Research and Community Service'}
                    </p>
                  </div>
                  <p className="footer-description text-light mb-3 text-center">
                    {organization?.description || 'A comprehensive platform for managing academic events, conferences, and institutional gatherings at Ethiopia\'s premier university.'}
                  </p>
                  <div className="footer-social d-flex gap-3 justify-content-center">
                    {organization ? (
                      <>
                        {organization.facebook_url && (
                          <a href={organization.facebook_url} target="_blank" rel="noopener noreferrer" className="social-link">
                            <i className="fab fa-facebook-f"></i>
                          </a>
                        )}
                        {organization.twitter_url && (
                          <a href={organization.twitter_url} target="_blank" rel="noopener noreferrer" className="social-link">
                            <i className="fab fa-twitter"></i>
                          </a>
                        )}
                        {organization.linkedin_url && (
                          <a href={organization.linkedin_url} target="_blank" rel="noopener noreferrer" className="social-link">
                            <i className="fab fa-linkedin-in"></i>
                          </a>
                        )}
                        {organization.instagram_url && (
                          <a href={organization.instagram_url} target="_blank" rel="noopener noreferrer" className="social-link">
                            <i className="fab fa-instagram"></i>
                          </a>
                        )}
                        {organization.website && (
                          <a href={organization.website} target="_blank" rel="noopener noreferrer" className="social-link">
                            <i className="fas fa-globe"></i>
                          </a>
                        )}
                      </>
                    ) : (
                      <>
                        <a href="https://www.uog.edu.et" target="_blank" rel="noopener noreferrer" className="social-link">
                          <i className="fas fa-globe"></i>
                        </a>
                        <a href="https://www.facebook.com/universityofgondar" target="_blank" rel="noopener noreferrer" className="social-link">
                          <i className="fab fa-facebook-f"></i>
                        </a>
                        <a href="https://twitter.com/uogondar" target="_blank" rel="noopener noreferrer" className="social-link">
                          <i className="fab fa-twitter"></i>
                        </a>
                        <a href="https://www.linkedin.com/school/university-of-gondar" target="_blank" rel="noopener noreferrer" className="social-link">
                          <i className="fab fa-linkedin-in"></i>
                        </a>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="col-lg-4">
                <h6 className="fw-bold mb-3 text-white">Quick Links</h6>
                <div className="footer-links">
                  <Link to="/events" className="footer-link d-block mb-2">
                    <i className="fas fa-calendar-alt me-2"></i>
                    All Events
                  </Link>
                  <Link to="/participant-register" className="footer-link d-block mb-2">
                    <i className="fas fa-user-plus me-2"></i>
                    Register for Events
                  </Link>
                  <a href="#university" className="footer-link d-block mb-2">
                    <i className="fas fa-info-circle me-2"></i>
                    About University
                  </a>
                  <a href="#campus-life" className="footer-link d-block mb-2">
                    <i className="fas fa-graduation-cap me-2"></i>
                    Campus Life
                  </a>

                </div>
              </div>

              <div className="col-lg-4">
                <h6 className="fw-bold mb-3 text-white">Contact Information</h6>
                <div className="contact-info">
                  <div className="contact-item d-flex align-items-center mb-2">
                    <i className="fas fa-envelope text-primary me-3"></i>
                    <span className="text-light">{organization?.email || '<EMAIL>'}</span>
                  </div>
                  <div className="contact-item d-flex align-items-center mb-2">
                    <i className="fas fa-phone text-primary me-3"></i>
                    <span className="text-light">{organization?.phone || '+251-58-114-1240'}</span>
                  </div>
                  <div className="contact-item d-flex align-items-center mb-2">
                    <i className="fas fa-map-marker-alt text-primary me-3"></i>
                    <span className="text-light">{organization?.full_address || 'Gondar, Amhara Region, Ethiopia'}</span>
                  </div>
                  <div className="contact-item d-flex align-items-center mb-2">
                    <i className="fas fa-globe text-primary me-3"></i>
                    <a href={organization?.website || 'https://www.uog.edu.et'} target="_blank" rel="noopener noreferrer" className="footer-link">
                      {organization?.website?.replace('https://', '').replace('http://', '') || 'www.uog.edu.et'}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="footer-bottom py-3">
          <div className="section-container">
            <div className="row align-items-center">
              <div className="col-md-6">
                <p className="footer-copyright mb-0 text-white">
                  © {new Date().getFullYear()} {organization?.name || 'University of Gondar'}. All rights reserved.
                </p>
              </div>
              <div className="col-md-6 text-md-end">
                <div className="d-flex align-items-center justify-content-md-end justify-content-center">
                  <span className="text-white me-3">Developed by</span>
                  <div className="developers-list d-flex gap-2">
                    {developers && developers.length > 0 ? developers.map((developer) => (
                      <div
                        key={developer.id}
                        className="developer-item position-relative"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title={`${developer.full_name} - ${developer.profession}`}
                      >
                        {developer.photo_url ? (
                          <img
                            src={developer.photo_url}
                            alt={developer.full_name}
                            className="developer-photo rounded-circle"
                            style={{ width: '30px', height: '30px', objectFit: 'cover' }}
                          />
                        ) : (
                          <div
                            className="developer-avatar rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-bold"
                            style={{ width: '30px', height: '30px', fontSize: '0.7rem' }}
                          >
                            {developer.full_name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                          </div>
                        )}

                        {/* Hover Card */}
                        <div className="developer-card position-absolute bg-white text-dark p-3 rounded-3 shadow-lg">
                          <div className="d-flex align-items-center mb-2">
                            {developer.photo_url ? (
                              <img
                                src={developer.photo_url}
                                alt={developer.full_name}
                                className="rounded-circle me-3"
                                style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                              />
                            ) : (
                              <div
                                className="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-bold me-3"
                                style={{ width: '50px', height: '50px' }}
                              >
                                {developer.full_name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                              </div>
                            )}
                            <div>
                              <h6 className="mb-1 fw-bold">{developer.full_name}</h6>
                              <p className="mb-0 text-muted small">{developer.profession}</p>
                            </div>
                          </div>
                          {developer.bio && (
                            <p className="small text-muted mb-2">{developer.bio}</p>
                          )}
                          {developer.linkedin_link && (
                            <a
                              href={developer.linkedin_link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="btn btn-sm btn-outline-primary"
                            >
                              <i className="fab fa-linkedin me-1"></i>
                              LinkedIn
                            </a>
                          )}
                        </div>
                      </div>
                    )) : (
                      <span className="text-light small">Loading...</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;

from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import logout
from django.db.models import Q
from django.utils.decorators import method_decorator
from event_management.rate_limiting import login_rate_limit, api_rate_limit, email_key_func
from .models import CustomUser, UserRole, UserSession, LoginAttempt, Developer
from .serializers import (
    CustomUserSerializer, UserCreateSerializer, UserUpdateSerializer,
    PasswordChangeSerializer, CustomTokenObtainPairSerializer,
    UserRoleSerializer, UserSessionSerializer, LoginAttemptSerializer, DeveloperSerializer
)

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

@method_decorator(login_rate_limit, name='post')
class LoginView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = CustomTokenObtainPairSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            return Response(serializer.validated_data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class LogoutView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        try:
            # Deactivate user session
            session_key = request.auth.token[:40] if hasattr(request.auth, 'token') else None
            if session_key:
                UserSession.objects.filter(
                    user=request.user,
                    session_key=session_key
                ).update(is_active=False)
            
            # Blacklist refresh token if provided
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            
            return Response({'message': 'Successfully logged out'}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

class UserProfileView(generics.RetrieveUpdateAPIView):
    serializer_class = CustomUserSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        return self.request.user

class ChangePasswordView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = PasswordChangeSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            user = request.user
            user.set_password(serializer.validated_data['new_password'])
            user.save()
            return Response({'message': 'Password changed successfully'}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class UserListCreateView(generics.ListCreateAPIView):
    queryset = CustomUser.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return UserCreateSerializer
        return CustomUserSerializer
    
    def get_queryset(self):
        # Only admins can see all users
        if self.request.user.can_manage_users():
            return CustomUser.objects.all().select_related('role')
        return CustomUser.objects.filter(id=self.request.user.id)
    
    def perform_create(self, serializer):
        # Only admins can create users
        if not self.request.user.can_manage_users():
            raise permissions.PermissionDenied("You don't have permission to create users")
        serializer.save()

class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = CustomUser.objects.all()
    serializer_class = UserUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # Users can only access their own profile, admins can access all
        if self.request.user.can_manage_users():
            return CustomUser.objects.all()
        return CustomUser.objects.filter(id=self.request.user.id)

class UserRoleListView(generics.ListAPIView):
    queryset = UserRole.objects.all()
    serializer_class = UserRoleSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # Only admins can see all roles
        if self.request.user.can_manage_users():
            return UserRole.objects.all()
        return UserRole.objects.none()

class UserSessionListView(generics.ListAPIView):
    serializer_class = UserSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # Users can see their own sessions, admins can see all
        if self.request.user.can_manage_users():
            return UserSession.objects.all().select_related('user')
        return UserSession.objects.filter(user=self.request.user)

class LoginAttemptListView(generics.ListAPIView):
    serializer_class = LoginAttemptSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # Only admins can see login attempts
        if self.request.user.can_manage_users():
            return LoginAttempt.objects.all().order_by('-timestamp')
        return LoginAttempt.objects.none()

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_permissions(request):
    """Get current user's permissions - simplified to use only Django superuser"""
    user = request.user

    # For superusers, grant all permissions
    # For regular users, grant no special permissions
    is_admin = user.is_superuser

    permissions_data = {
        'user_id': user.id,
        'username': user.username,
        'role': 'administrator' if is_admin else 'user',
        'role_display': 'Administrator' if is_admin else 'User',
        'permissions': {
            'can_manage_users': is_admin,
            'can_manage_events': is_admin,
            'can_manage_participants': is_admin,
            'can_take_attendance': is_admin,
            'can_upload_gallery': is_admin,
            'can_manage_schedule': is_admin,
            'can_generate_badges': is_admin,
            'can_manage_hotels': is_admin,
            'can_manage_drivers': is_admin,
            'can_view_reports': is_admin,
            'can_export_data': is_admin,
        }
    }
    return Response(permissions_data)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def dashboard_stats(request):
    """Get dashboard statistics based on user role"""
    user = request.user
    stats = {}
    
    if user.is_superuser:
        # Admin stats
        stats.update({
            'total_users': CustomUser.objects.count(),
            'active_users': CustomUser.objects.filter(is_active=True).count(),
            'total_sessions': UserSession.objects.filter(is_active=True).count(),
            'recent_logins': LoginAttempt.objects.filter(success=True).count(),
        })
    
    if user.is_superuser:
        from events.models import Event
        from django.utils import timezone
        stats.update({
            'total_events': Event.objects.count(),
            'active_events': Event.objects.filter(
                start_date__lte=timezone.now(),
                end_date__gte=timezone.now()
            ).count(),
        })
    
    if user.is_superuser:
        from participants.models import Participant
        stats.update({
            'total_participants': Participant.objects.count(),
            'confirmed_participants': Participant.objects.filter(is_confirmed=True).count(),
        })
    
    if user.is_superuser:
        from participants.models import Attendance
        from django.utils import timezone
        stats.update({
            'total_attendance': Attendance.objects.count(),
            'today_attendance': Attendance.objects.filter(
                checked_in_at__date=timezone.now().date()
            ).count(),
        })
    
    if user.is_superuser:
        from events.models import EventGallery
        stats.update({
            'total_photos': EventGallery.objects.count(),
            'featured_photos': EventGallery.objects.filter(is_featured=True).count(),
        })
    
    return Response(stats)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_permissions(request):
    """Get current user's permissions"""
    user = request.user

    # Get user role permissions
    role_permissions = {}
    if hasattr(user, 'role') and user.role:
        role_permissions = {
            'can_manage_users': user.role.can_manage_users,
            'can_manage_events': user.role.can_manage_events,
            'can_manage_participants': user.role.can_manage_participants,
            'can_take_attendance': user.role.can_take_attendance,
            'can_upload_gallery': user.role.can_upload_gallery,
            'can_manage_schedule': user.role.can_manage_schedule,
            'can_generate_badges': user.role.can_generate_badges,
            'can_manage_hotels': user.role.can_manage_hotels,
            'can_manage_drivers': user.role.can_manage_drivers,
            'can_view_reports': user.role.can_view_reports,
            'can_export_data': user.role.can_export_data,
        }

    # Add Django permissions
    django_permissions = {
        'is_superuser': user.is_superuser,
        'is_staff': user.is_staff,
        'is_active': user.is_active,
    }

    return Response({
        'role_permissions': role_permissions,
        'django_permissions': django_permissions,
        'role_name': user.role.name if hasattr(user, 'role') and user.role else None,
        'role_display_name': user.role.display_name if hasattr(user, 'role') and user.role else None,
    })


class DeveloperListView(generics.ListAPIView):
    """Public API to get list of developers for the footer"""
    queryset = Developer.objects.filter(is_active=True).order_by('order', 'full_name')
    serializer_class = DeveloperSerializer
    permission_classes = [permissions.AllowAny]  # Public access for footer

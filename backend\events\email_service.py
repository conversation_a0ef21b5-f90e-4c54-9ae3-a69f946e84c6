import os
import zipfile
import tempfile
from datetime import datetime, timedelta
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from .models import EmailConfiguration, EmailTemplate, EmailLog, EventGallery
from participants.models import Participant


class EmailService:
    """Service class for handling email operations"""
    
    def __init__(self):
        self.config = self._get_active_config()
    
    def _get_active_config(self):
        """Get the active email configuration"""
        try:
            return EmailConfiguration.objects.get(is_active=True)
        except EmailConfiguration.DoesNotExist:
            return None
    
    def _get_template(self, template_type):
        """Get the active email template for a specific type"""
        try:
            return EmailTemplate.objects.get(template_type=template_type, is_active=True)
        except EmailTemplate.DoesNotExist:
            return None
    
    def _log_email(self, recipient_email, recipient_name, subject, template_type, status='pending', error_message=''):
        """Log email sending attempt"""
        return EmailLog.objects.create(
            recipient_email=recipient_email,
            recipient_name=recipient_name,
            subject=subject,
            template_type=template_type,
            status=status,
            error_message=error_message,
            sent_at=timezone.now() if status == 'sent' else None
        )
    
    def send_email(self, template_type, recipient_email, recipient_name='', context=None, attachments=None):
        """
        Send email using the specified template type
        
        Args:
            template_type: Type of email template to use
            recipient_email: Recipient's email address
            recipient_name: Recipient's name (optional)
            context: Dictionary of context variables for template rendering
            attachments: List of file paths to attach
        
        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        if not self.config:
            print("No active email configuration found")
            return False
        
        template = self._get_template(template_type)
        if not template:
            print(f"No active template found for type: {template_type}")
            return False
        
        context = context or {}
        context.update({
            'recipient_name': recipient_name,
            'site_name': 'University of Gondar Events',
            'current_year': datetime.now().year,
        })
        
        # Render email content
        try:
            subject = template.subject.format(**context)
            html_content = template.html_content.format(**context)
            text_content = template.text_content.format(**context) if template.text_content else None
        except KeyError as e:
            error_msg = f"Template rendering error: Missing context variable {e}"
            self._log_email(recipient_email, recipient_name, template.subject, template_type, 'failed', error_msg)
            print(error_msg)
            return False
        
        # Create email log entry
        email_log = self._log_email(recipient_email, recipient_name, subject, template_type)
        
        try:
            # Create email message
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content or html_content,
                from_email=self.config.default_from_email,
                to=[recipient_email]
            )
            
            if html_content:
                email.attach_alternative(html_content, "text/html")
            
            # Add attachments if provided
            if attachments:
                for attachment_path in attachments:
                    if os.path.exists(attachment_path):
                        email.attach_file(attachment_path)
            
            # Send email
            email.send()
            
            # Update log status
            email_log.status = 'sent'
            email_log.sent_at = timezone.now()
            email_log.save()
            
            return True
            
        except Exception as e:
            error_msg = str(e)
            email_log.status = 'failed'
            email_log.error_message = error_msg
            email_log.save()
            print(f"Failed to send email: {error_msg}")
            return False
    
    def send_registration_confirmation(self, participant):
        """Send registration confirmation email"""
        context = {
            'participant_name': participant.full_name,
            'event_name': participant.event.name,
            'event_date': participant.event.start_date.strftime('%B %d, %Y'),
            'event_location': participant.event.location,
            'registration_date': participant.registration_date.strftime('%B %d, %Y'),
        }
        
        return self.send_email(
            'registration_confirmation',
            participant.email,
            participant.full_name,
            context
        )
    
    def send_event_details(self, participant):
        """Send detailed event information"""
        context = {
            'participant_name': participant.full_name,
            'event_name': participant.event.name,
            'event_description': participant.event.description,
            'event_start_date': participant.event.start_date.strftime('%B %d, %Y at %I:%M %p'),
            'event_end_date': participant.event.end_date.strftime('%B %d, %Y at %I:%M %p'),
            'event_location': participant.event.location,
            'organizer_name': participant.event.organizer_name,
            'organizer_email': participant.event.organizer_email,
            'organizer_phone': participant.event.organizer_phone,
        }

        return self.send_email(
            'event_details',
            participant.email,
            participant.full_name,
            context
        )
    
    def send_badge_notification(self, participant, badge_path=None):
        """Send badge notification with attachment"""
        context = {
            'participant_name': participant.full_name,
            'event_name': participant.event.name,
        }

        attachments = [badge_path] if badge_path and os.path.exists(badge_path) else None

        return self.send_email(
            'badge_notification',
            participant.email,
            participant.full_name,
            context,
            attachments
        )
    
    def send_schedule_update(self, participants, event):
        """Send schedule updates to multiple participants"""
        context = {
            'event_name': event.name,
            'event_date': event.start_date.strftime('%B %d, %Y'),
            'schedules': event.schedules.filter(is_active=True).order_by('start_time'),
        }
        
        success_count = 0
        for participant in participants:
            participant_context = context.copy()
            participant_context['participant_name'] = participant.full_name

            if self.send_email(
                'schedule_update',
                participant.email,
                participant.full_name,
                participant_context
            ):
                success_count += 1
        
        return success_count
    
    def send_daily_gallery(self, participants, event, date=None):
        """Send daily event gallery images in a zip file"""
        if not date:
            date = timezone.now().date()
        
        # Get gallery images for the specific date
        gallery_images = EventGallery.objects.filter(
            event=event,
            uploaded_at__date=date
        )
        
        if not gallery_images.exists():
            print(f"No gallery images found for {date}")
            return 0
        
        # Create zip file with images
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip:
            with zipfile.ZipFile(temp_zip.name, 'w') as zip_file:
                for image in gallery_images:
                    if os.path.exists(image.image.path):
                        # Add image to zip with a clean filename
                        filename = f"{image.title}_{image.id}.jpg"
                        zip_file.write(image.image.path, filename)
            
            zip_path = temp_zip.name
        
        context = {
            'event_name': event.name,
            'date': date.strftime('%B %d, %Y'),
            'image_count': gallery_images.count(),
        }
        
        success_count = 0
        for participant in participants:
            participant_context = context.copy()
            participant_context['participant_name'] = participant.full_name

            if self.send_email(
                'daily_gallery',
                participant.email,
                participant.full_name,
                participant_context,
                [zip_path]
            ):
                success_count += 1
        
        # Clean up temporary zip file
        try:
            os.unlink(zip_path)
        except OSError:
            pass
        
        return success_count
    
    def send_event_reminder(self, participants, event, days_before=1):
        """Send event reminder to participants"""
        context = {
            'event_name': event.name,
            'event_date': event.start_date.strftime('%B %d, %Y at %I:%M %p'),
            'event_location': event.location,
            'days_before': days_before,
        }
        
        success_count = 0
        for participant in participants:
            participant_context = context.copy()
            participant_context['participant_name'] = participant.full_name

            if self.send_email(
                'event_reminder',
                participant.email,
                participant.full_name,
                participant_context
            ):
                success_count += 1
        
        return success_count


# Convenience function for easy import
def get_email_service():
    """Get an instance of EmailService"""
    return EmailService()

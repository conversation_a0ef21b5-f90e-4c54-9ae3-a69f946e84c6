# University of Gondar Event Management System - Makefile

.PHONY: help local-build local-up local-down local-logs local-shell prod-build prod-up prod-down prod-logs backup restore clean

# Default target
help:
	@echo "University of Gondar Event Management System"
	@echo "============================================"
	@echo ""
	@echo "Available commands:"
	@echo ""
	@echo "Local Development:"
	@echo "  local-build    Build local development images"
	@echo "  local-up       Start local development environment"
	@echo "  local-down     Stop local development environment"
	@echo "  local-logs     View local development logs"
	@echo "  local-shell    Access backend shell (local)"
	@echo "  local-migrate  Run database migrations (local)"
	@echo "  local-test     Run tests (local)"
	@echo ""
	@echo "Production:"
	@echo "  prod-build     Build production images"
	@echo "  prod-up        Start production environment"
	@echo "  prod-down      Stop production environment"
	@echo "  prod-logs      View production logs"
	@echo "  prod-shell     Access backend shell (production)"
	@echo "  prod-migrate   Run database migrations (production)"
	@echo ""
	@echo "Maintenance:"
	@echo "  backup         Create database backup"
	@echo "  restore        Restore database from backup"
	@echo "  clean          Clean up Docker resources"
	@echo "  health         Check service health"
	@echo ""

# Local Development Commands
local-build:
	@echo "Building local development images..."
	docker-compose -f docker-compose.local.yml build

local-up:
	@echo "Starting local development environment..."
	docker-compose -f docker-compose.local.yml up -d
	@echo "Services started. Frontend: http://localhost:3001, Backend: http://localhost:8000"

local-down:
	@echo "Stopping local development environment..."
	docker-compose -f docker-compose.local.yml down

local-logs:
	@echo "Viewing local development logs..."
	docker-compose -f docker-compose.local.yml logs -f

local-shell:
	@echo "Accessing backend shell (local)..."
	docker-compose -f docker-compose.local.yml exec backend python manage.py shell

local-migrate:
	@echo "Running database migrations (local)..."
	docker-compose -f docker-compose.local.yml exec backend python manage.py migrate

local-test:
	@echo "Running tests (local)..."
	docker-compose -f docker-compose.local.yml exec backend python manage.py test

# Production Commands
prod-build:
	@echo "Building production images..."
	docker-compose -f docker-compose.prod.yml build --no-cache

prod-up:
	@echo "Starting production environment..."
	docker-compose -f docker-compose.prod.yml up -d
	@echo "Production services started. Website: https://event.uog.edu.et"

prod-down:
	@echo "Stopping production environment..."
	docker-compose -f docker-compose.prod.yml down

prod-logs:
	@echo "Viewing production logs..."
	docker-compose -f docker-compose.prod.yml logs -f

prod-shell:
	@echo "Accessing backend shell (production)..."
	docker-compose -f docker-compose.prod.yml exec backend python manage.py shell

prod-migrate:
	@echo "Running database migrations (production)..."
	docker-compose -f docker-compose.prod.yml exec backend python manage.py migrate

# Maintenance Commands
backup:
	@echo "Creating database backup..."
	@mkdir -p backups
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then \
		docker-compose -f docker-compose.prod.yml exec -T db pg_dump -U $$POSTGRES_USER $$POSTGRES_DB > backups/backup_$$timestamp.sql; \
		echo "Production backup created: backups/backup_$$timestamp.sql"; \
	elif docker-compose -f docker-compose.local.yml ps | grep -q "Up"; then \
		docker-compose -f docker-compose.local.yml exec -T db pg_dump -U postgres uog_event_local > backups/backup_local_$$timestamp.sql; \
		echo "Local backup created: backups/backup_local_$$timestamp.sql"; \
	else \
		echo "No running database found"; \
	fi

restore:
	@echo "Restoring database from backup..."
	@read -p "Enter backup file path: " backup_file; \
	if [ -f "$$backup_file" ]; then \
		if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then \
			docker-compose -f docker-compose.prod.yml exec -T db psql -U $$POSTGRES_USER $$POSTGRES_DB < $$backup_file; \
			echo "Production database restored from $$backup_file"; \
		elif docker-compose -f docker-compose.local.yml ps | grep -q "Up"; then \
			docker-compose -f docker-compose.local.yml exec -T db psql -U postgres uog_event_local < $$backup_file; \
			echo "Local database restored from $$backup_file"; \
		else \
			echo "No running database found"; \
		fi \
	else \
		echo "Backup file not found: $$backup_file"; \
	fi

clean:
	@echo "Cleaning up Docker resources..."
	docker system prune -f
	docker volume prune -f
	@echo "Cleanup completed"

health:
	@echo "Checking service health..."
	@if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then \
		echo "Production services:"; \
		docker-compose -f docker-compose.prod.yml ps; \
		echo ""; \
		echo "Health checks:"; \
		curl -f https://event.uog.edu.et/health 2>/dev/null && echo "✓ Website is accessible" || echo "✗ Website is not accessible"; \
		curl -f https://event.uog.edu.et/api/ 2>/dev/null && echo "✓ API is accessible" || echo "✗ API is not accessible"; \
	elif docker-compose -f docker-compose.local.yml ps | grep -q "Up"; then \
		echo "Local services:"; \
		docker-compose -f docker-compose.local.yml ps; \
		echo ""; \
		echo "Health checks:"; \
		curl -f http://localhost:3001 2>/dev/null && echo "✓ Frontend is accessible" || echo "✗ Frontend is not accessible"; \
		curl -f http://localhost:8000/api/ 2>/dev/null && echo "✓ Backend API is accessible" || echo "✗ Backend API is not accessible"; \
	else \
		echo "No services are running"; \
	fi

# Quick commands
dev: local-up
prod: prod-up
stop: local-down prod-down
logs: local-logs

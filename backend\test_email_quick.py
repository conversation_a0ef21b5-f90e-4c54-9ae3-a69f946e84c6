#!/usr/bin/env python
import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from events.models import EmailLog, EmailConfiguration
from participants.models import Participant

def main():
    print("=== Email System Status Check ===")
    
    # Check email configuration
    configs = EmailConfiguration.objects.all()
    active_config = EmailConfiguration.objects.filter(is_active=True).first()
    
    print(f"Total email configurations: {configs.count()}")
    if active_config:
        print(f"Active configuration: {active_config.name}")
        print(f"  Host: {active_config.email_host}")
        print(f"  User: {active_config.email_host_user}")
        print(f"  Port: {active_config.email_port}")
        print(f"  TLS: {active_config.email_use_tls}")
    else:
        print("❌ No active email configuration found!")
        return
    
    print()
    
    # Check recent email logs
    print("=== Recent Email Logs (Last 10) ===")
    logs = EmailLog.objects.order_by('-created_at')[:10]
    for log in logs:
        status_icon = "✅" if log.status == 'sent' else "❌" if log.status == 'failed' else "⏳"
        print(f"{status_icon} {log.created_at.strftime('%Y-%m-%d %H:%M:%S')}: {log.recipient_email}")
        print(f"   Subject: {log.subject}")
        print(f"   Status: {log.status}")
        if log.error_message:
            print(f"   Error: {log.error_message}")
        print()
    
    # Check pending emails
    pending = EmailLog.objects.filter(status='pending').count()
    failed = EmailLog.objects.filter(status='failed').count()
    sent = EmailLog.objects.filter(status='sent').count()
    
    print(f"=== Email Statistics ===")
    print(f"Sent: {sent}")
    print(f"Failed: {failed}")
    print(f"Pending: {pending}")
    
    # Test email service
    print("\n=== Testing Email Service ===")
    try:
        from events.email_service import get_email_service
        service = get_email_service()
        if service.config:
            print("✅ Email service initialized successfully")
            print(f"   Using config: {service.config.name}")
        else:
            print("❌ Email service failed to initialize - no active config")
    except Exception as e:
        print(f"❌ Email service error: {e}")

if __name__ == "__main__":
    main()

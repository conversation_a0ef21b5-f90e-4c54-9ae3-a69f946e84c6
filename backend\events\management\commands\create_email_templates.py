from django.core.management.base import BaseCommand
from events.models import EmailTemplate


class Command(BaseCommand):
    help = 'Create default email templates'
    
    def handle(self, *args, **options):
        templates = [
            {
                'name': 'Registration Confirmation',
                'template_type': 'registration_confirmation',
                'subject': 'Registration Confirmed - {event_name}',
                'html_content': '''
                <h2>Welcome {participant_name}!</h2>
                <p>Thank you for registering for <strong>{event_name}</strong>.</p>
                
                <div class="highlight-box">
                    <h3>Event Details:</h3>
                    <p><strong>Event:</strong> {event_name}</p>
                    <p><strong>Date:</strong> {event_date}</p>
                    <p><strong>Location:</strong> {event_location}</p>
                    <p><strong>Registration Date:</strong> {registration_date}</p>
                </div>
                
                <p>We will send you more details about the event soon, including your badge and schedule.</p>
                <p>If you have any questions, please don't hesitate to contact us.</p>
                
                <p>Best regards,<br>University of Gondar Events Team</p>
                ''',
                'text_content': '''
                Welcome {participant_name}!
                
                Thank you for registering for {event_name}.
                
                Event Details:
                - Event: {event_name}
                - Date: {event_date}
                - Location: {event_location}
                - Registration Date: {registration_date}
                
                We will send you more details about the event soon.
                
                Best regards,
                University of Gondar Events Team
                '''
            },
            {
                'name': 'Event Details',
                'template_type': 'event_details',
                'subject': 'Event Details - {event_name}',
                'html_content': '''
                <h2>Event Details for {participant_name}</h2>
                <p>Here are the complete details for <strong>{event_name}</strong>:</p>
                
                <div class="highlight-box">
                    <h3>{event_name}</h3>
                    <p>{event_description}</p>
                    <p><strong>Start:</strong> {event_start_date}</p>
                    <p><strong>End:</strong> {event_end_date}</p>
                    <p><strong>Location:</strong> {event_location}</p>
                </div>
                
                <h3>Organizer Information:</h3>
                <p><strong>Name:</strong> {organizer_name}</p>
                <p><strong>Email:</strong> {organizer_email}</p>
                <p><strong>Phone:</strong> {organizer_phone}</p>
                
                <p>We look forward to seeing you at the event!</p>
                
                <p>Best regards,<br>University of Gondar Events Team</p>
                ''',
                'text_content': '''
                Event Details for {participant_name}
                
                {event_name}
                {event_description}
                
                Start: {event_start_date}
                End: {event_end_date}
                Location: {event_location}
                
                Organizer: {organizer_name}
                Email: {organizer_email}
                Phone: {organizer_phone}
                
                Best regards,
                University of Gondar Events Team
                '''
            },
            {
                'name': 'Badge Notification',
                'template_type': 'badge_notification',
                'subject': 'Your Event Badge - {event_name}',
                'html_content': '''
                <h2>Your Event Badge is Ready!</h2>
                <p>Dear {participant_name},</p>
                
                <p>Your badge for <strong>{event_name}</strong> has been generated and is attached to this email.</p>
                
                <div class="highlight-box">
                    <h3>Important Instructions:</h3>
                    <ul>
                        <li>Please print your badge on quality paper</li>
                        <li>Bring your badge to the event for identification</li>
                        <li>Keep your badge visible throughout the event</li>
                        <li>Your badge contains a QR code for quick check-in</li>
                    </ul>
                </div>
                
                <p>If you have any issues with your badge, please contact us immediately.</p>
                
                <p>Best regards,<br>University of Gondar Events Team</p>
                ''',
                'text_content': '''
                Your Event Badge is Ready!
                
                Dear {participant_name},
                
                Your badge for {event_name} has been generated and is attached to this email.
                
                Important Instructions:
                - Please print your badge on quality paper
                - Bring your badge to the event for identification
                - Keep your badge visible throughout the event
                - Your badge contains a QR code for quick check-in
                
                Best regards,
                University of Gondar Events Team
                '''
            },
            {
                'name': 'Schedule Update',
                'template_type': 'schedule_update',
                'subject': 'Updated Schedule - {event_name}',
                'html_content': '''
                <h2>Schedule Update</h2>
                <p>Dear {participant_name},</p>
                
                <p>The schedule for <strong>{event_name}</strong> has been updated. Please review the latest schedule below:</p>
                
                <div class="highlight-box">
                    <h3>Event Schedule - {event_date}</h3>
                    <p>Please check the event portal for the most up-to-date schedule information.</p>
                </div>
                
                <p>Make sure to arrive on time for all sessions you plan to attend.</p>
                
                <p>Best regards,<br>University of Gondar Events Team</p>
                ''',
                'text_content': '''
                Schedule Update
                
                Dear {participant_name},
                
                The schedule for {event_name} has been updated.
                Event Date: {event_date}
                
                Please check the event portal for the most up-to-date schedule information.
                
                Best regards,
                University of Gondar Events Team
                '''
            },
            {
                'name': 'Daily Gallery',
                'template_type': 'daily_gallery',
                'subject': 'Daily Event Photos - {event_name}',
                'html_content': '''
                <h2>Daily Event Photos</h2>
                <p>Dear {participant_name},</p>
                
                <p>Here are the photos from <strong>{event_name}</strong> for {date}.</p>
                
                <div class="highlight-box">
                    <h3>Photo Package</h3>
                    <p>📸 <strong>{image_count}</strong> photos from {date}</p>
                    <p>All photos are included in the attached ZIP file.</p>
                </div>
                
                <p>Feel free to download, share, and enjoy these memories from the event!</p>
                
                <p>Best regards,<br>University of Gondar Events Team</p>
                ''',
                'text_content': '''
                Daily Event Photos
                
                Dear {participant_name},
                
                Here are the photos from {event_name} for {date}.
                
                Photo Package: {image_count} photos from {date}
                All photos are included in the attached ZIP file.
                
                Best regards,
                University of Gondar Events Team
                '''
            },
            {
                'name': 'Event Reminder',
                'template_type': 'event_reminder',
                'subject': 'Reminder: {event_name} in {days_before} day(s)',
                'html_content': '''
                <h2>Event Reminder</h2>
                <p>Dear {participant_name},</p>
                
                <p>This is a friendly reminder that <strong>{event_name}</strong> is coming up!</p>
                
                <div class="highlight-box">
                    <h3>Event Information:</h3>
                    <p><strong>Date & Time:</strong> {event_date}</p>
                    <p><strong>Location:</strong> {event_location}</p>
                    <p><strong>Days Remaining:</strong> {days_before}</p>
                </div>
                
                <h3>Don't Forget:</h3>
                <ul>
                    <li>Print and bring your event badge</li>
                    <li>Arrive 15 minutes early for check-in</li>
                    <li>Bring any required materials</li>
                    <li>Check the latest schedule updates</li>
                </ul>
                
                <p>We're excited to see you at the event!</p>
                
                <p>Best regards,<br>University of Gondar Events Team</p>
                ''',
                'text_content': '''
                Event Reminder
                
                Dear {participant_name},
                
                This is a friendly reminder that {event_name} is coming up!
                
                Date & Time: {event_date}
                Location: {event_location}
                Days Remaining: {days_before}
                
                Don't Forget:
                - Print and bring your event badge
                - Arrive 15 minutes early for check-in
                - Bring any required materials
                - Check the latest schedule updates
                
                Best regards,
                University of Gondar Events Team
                '''
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for template_data in templates:
            template, created = EmailTemplate.objects.get_or_create(
                template_type=template_data['template_type'],
                defaults=template_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            else:
                # Update existing template
                for key, value in template_data.items():
                    setattr(template, key, value)
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated template: {template.name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed {created_count} new templates and updated {updated_count} existing templates'
            )
        )

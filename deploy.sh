#!/bin/bash

# University of Gondar Event Management System
# Production Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="University of Gondar Event Management System"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
BACKUP_DIR="./backups"
LOG_FILE="./deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a $LOG_FILE
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a $LOG_FILE
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a $LOG_FILE
}

banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║        🏛️  University of Gondar Event Management 🏛️         ║"
    echo "║                                                              ║"
    echo "║                    Production Deployment                     ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

check_requirements() {
    log "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check environment file
    if [ ! -f "$ENV_FILE" ]; then
        warning "Environment file not found. Creating from template..."
        if [ -f ".env.example" ]; then
            cp .env.example $ENV_FILE
            warning "Please edit $ENV_FILE with your production settings before continuing."
            exit 1
        else
            error "No environment template found. Please create $ENV_FILE manually."
            exit 1
        fi
    fi
    
    success "System requirements check passed"
}

backup_data() {
    log "Creating backup of existing data..."
    
    # Create backup directory
    mkdir -p $BACKUP_DIR
    
    # Backup database if container exists
    if docker-compose ps | grep -q "db"; then
        log "Backing up database..."
        docker-compose exec -T db pg_dump -U postgres event_management > "$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
        success "Database backup created"
    fi
    
    # Backup media files if they exist
    if [ -d "./backend/media" ]; then
        log "Backing up media files..."
        tar -czf "$BACKUP_DIR/media_backup_$(date +%Y%m%d_%H%M%S).tar.gz" ./backend/media
        success "Media files backup created"
    fi
}

build_images() {
    log "Building Docker images..."
    
    # Build images with no cache for production
    docker-compose build --no-cache --parallel
    
    success "Docker images built successfully"
}

deploy_services() {
    log "Deploying services..."
    
    # Stop existing services
    docker-compose down
    
    # Start services in production mode
    docker-compose up -d
    
    # Wait for services to be healthy
    log "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    if docker-compose ps | grep -q "unhealthy\|Exit"; then
        error "Some services failed to start properly"
        docker-compose logs
        exit 1
    fi
    
    success "All services deployed successfully"
}

run_migrations() {
    log "Running database migrations..."
    
    # Wait for database to be ready
    docker-compose exec backend python manage.py migrate --noinput
    
    success "Database migrations completed"
}

create_superuser() {
    log "Creating superuser account..."
    
    # Create superuser using environment variables
    docker-compose exec backend python manage.py shell << EOF
from authentication.models import CustomUser, UserRole
import os

username = os.environ.get('ADMIN_USERNAME', 'admin')
email = os.environ.get('ADMIN_EMAIL', '<EMAIL>')
password = os.environ.get('ADMIN_PASSWORD', 'admin123')

if not CustomUser.objects.filter(username=username).exists():
    admin_role = UserRole.objects.get(name='admin')
    CustomUser.objects.create_superuser(
        username=username,
        email=email,
        password=password,
        first_name='System',
        last_name='Administrator',
        role=admin_role
    )
    print(f"Superuser '{username}' created successfully!")
else:
    print(f"Superuser '{username}' already exists.")
EOF
    
    success "Superuser account ready"
}

collect_static() {
    log "Collecting static files..."
    
    docker-compose exec backend python manage.py collectstatic --noinput
    
    success "Static files collected"
}

setup_ssl() {
    log "Setting up SSL certificates..."
    
    # Create SSL directory if it doesn't exist
    mkdir -p ./nginx/ssl
    
    # Check if SSL certificates exist
    if [ ! -f "./nginx/ssl/cert.pem" ] || [ ! -f "./nginx/ssl/key.pem" ]; then
        warning "SSL certificates not found. Generating self-signed certificates for development..."
        
        # Generate self-signed certificate
        openssl req -x509 -newkey rsa:4096 -keyout ./nginx/ssl/key.pem -out ./nginx/ssl/cert.pem -days 365 -nodes \
            -subj "/C=ET/ST=Amhara/L=Gondar/O=University of Gondar/CN=localhost"
        
        warning "Self-signed certificates generated. Replace with proper SSL certificates for production."
    fi
    
    success "SSL setup completed"
}

show_status() {
    log "Deployment Status:"
    echo ""
    docker-compose ps
    echo ""
    
    log "Service URLs:"
    echo -e "${CYAN}🌐 Frontend: http://localhost:3000${NC}"
    echo -e "${CYAN}🔧 Backend API: http://localhost:8000/api${NC}"
    echo -e "${CYAN}👑 Admin Panel: http://localhost:8000/admin${NC}"
    echo -e "${CYAN}📊 Health Check: http://localhost:8000/health${NC}"
    echo ""
    
    log "Default Admin Credentials:"
    echo -e "${YELLOW}Username: admin${NC}"
    echo -e "${YELLOW}Password: admin123${NC}"
    echo -e "${YELLOW}Email: <EMAIL>${NC}"
    echo ""
    
    warning "Please change the default admin password after first login!"
}

cleanup() {
    log "Cleaning up unused Docker resources..."
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes (be careful with this in production)
    # docker volume prune -f
    
    success "Cleanup completed"
}

main() {
    banner
    
    log "Starting deployment of $PROJECT_NAME"
    
    check_requirements
    backup_data
    build_images
    deploy_services
    run_migrations
    create_superuser
    collect_static
    setup_ssl
    cleanup
    
    success "🎉 Deployment completed successfully!"
    show_status
    
    log "Deployment log saved to: $LOG_FILE"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        backup_data
        ;;
    "status")
        show_status
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "stop")
        log "Stopping all services..."
        docker-compose down
        success "All services stopped"
        ;;
    "restart")
        log "Restarting all services..."
        docker-compose restart
        success "All services restarted"
        ;;
    "update")
        log "Updating application..."
        git pull
        docker-compose build --no-cache
        docker-compose up -d
        success "Application updated"
        ;;
    *)
        echo "Usage: $0 {deploy|backup|status|logs|stop|restart|update}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Full deployment (default)"
        echo "  backup  - Backup data only"
        echo "  status  - Show service status"
        echo "  logs    - Show service logs"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  update  - Update and redeploy"
        exit 1
        ;;
esac

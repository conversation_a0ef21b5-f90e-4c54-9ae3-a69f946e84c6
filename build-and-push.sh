#!/bin/bash

# Build and Push Docker Images to Docker Hub
# University of Gondar Events Management System

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_HUB_USERNAME=${DOCKER_HUB_USERNAME:-"uogevents"}
IMAGE_TAG=${IMAGE_TAG:-"latest"}
VERSION=${VERSION:-"1.0.0"}

# Image names
BACKEND_IMAGE="$DOCKER_HUB_USERNAME/uog-events-backend"
FRONTEND_IMAGE="$DOCKER_HUB_USERNAME/uog-events-frontend"
NGINX_IMAGE="$DOCKER_HUB_USERNAME/uog-events-nginx"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}UoG Events - Docker Build & Push${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${YELLOW}Docker Hub Username: $DOCKER_HUB_USERNAME${NC}"
echo -e "${YELLOW}Image Tag: $IMAGE_TAG${NC}"
echo -e "${YELLOW}Version: $VERSION${NC}"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

# Login to Docker Hub
echo -e "${BLUE}🔐 Logging in to Docker Hub...${NC}"
if ! docker login; then
    echo -e "${RED}❌ Failed to login to Docker Hub${NC}"
    exit 1
fi

# Build Backend Image
echo -e "${BLUE}🏗️  Building Backend Image...${NC}"
docker build -t "$BACKEND_IMAGE:$IMAGE_TAG" -t "$BACKEND_IMAGE:$VERSION" -f backend/Dockerfile.prod backend/
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Backend image built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build backend image${NC}"
    exit 1
fi

# Build Frontend Image
echo -e "${BLUE}🏗️  Building Frontend Image...${NC}"
docker build -t "$FRONTEND_IMAGE:$IMAGE_TAG" -t "$FRONTEND_IMAGE:$VERSION" \
    --build-arg REACT_APP_API_BASE_URL=https://event.uog.edu.et/api \
    --build-arg REACT_APP_BACKEND_URL=https://event.uog.edu.et \
    -f frontend/Dockerfile.prod frontend/
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Frontend image built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build frontend image${NC}"
    exit 1
fi

# Build Nginx Image
echo -e "${BLUE}🏗️  Building Nginx Image...${NC}"
docker build -t "$NGINX_IMAGE:$IMAGE_TAG" -t "$NGINX_IMAGE:$VERSION" nginx/
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Nginx image built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build nginx image${NC}"
    exit 1
fi

# Push Images to Docker Hub
echo -e "${BLUE}📤 Pushing images to Docker Hub...${NC}"

echo -e "${YELLOW}Pushing Backend Image...${NC}"
docker push "$BACKEND_IMAGE:$IMAGE_TAG"
docker push "$BACKEND_IMAGE:$VERSION"

echo -e "${YELLOW}Pushing Frontend Image...${NC}"
docker push "$FRONTEND_IMAGE:$IMAGE_TAG"
docker push "$FRONTEND_IMAGE:$VERSION"

echo -e "${YELLOW}Pushing Nginx Image...${NC}"
docker push "$NGINX_IMAGE:$IMAGE_TAG"
docker push "$NGINX_IMAGE:$VERSION"

# Display image information
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}✅ All images built and pushed successfully!${NC}"
echo -e "${GREEN}========================================${NC}"
echo ""
echo -e "${BLUE}📋 Image Information:${NC}"
echo -e "${YELLOW}Backend:${NC}  $BACKEND_IMAGE:$IMAGE_TAG"
echo -e "${YELLOW}Frontend:${NC} $FRONTEND_IMAGE:$IMAGE_TAG"
echo -e "${YELLOW}Nginx:${NC}    $NGINX_IMAGE:$IMAGE_TAG"
echo ""
echo -e "${BLUE}📋 Versioned Images:${NC}"
echo -e "${YELLOW}Backend:${NC}  $BACKEND_IMAGE:$VERSION"
echo -e "${YELLOW}Frontend:${NC} $FRONTEND_IMAGE:$VERSION"
echo -e "${YELLOW}Nginx:${NC}    $NGINX_IMAGE:$VERSION"
echo ""

# Create docker-compose.hub.yml for using Docker Hub images
echo -e "${BLUE}📝 Creating docker-compose.hub.yml...${NC}"
cat > docker-compose.hub.yml << EOF
version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: \${POSTGRES_DB}
      POSTGRES_USER: \${POSTGRES_USER}
      POSTGRES_PASSWORD: \${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U \${POSTGRES_USER} -d \${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --requirepass \${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django Backend
  backend:
    image: $BACKEND_IMAGE:$IMAGE_TAG
    restart: unless-stopped
    environment:
      - SECRET_KEY=\${SECRET_KEY}
      - DEBUG=\${DEBUG:-False}
      - ALLOWED_HOSTS=\${ALLOWED_HOSTS}
      - DATABASE_NAME=\${POSTGRES_DB}
      - DATABASE_USER=\${POSTGRES_USER}
      - DATABASE_PASSWORD=\${POSTGRES_PASSWORD}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:\${REDIS_PASSWORD}@redis:6379/0
      - CORS_ALLOWED_ORIGINS=\${CORS_ALLOWED_ORIGINS}
      - QR_CODE_BASE_URL=\${QR_CODE_BASE_URL}
      - EMAIL_HOST=\${EMAIL_HOST}
      - EMAIL_PORT=\${EMAIL_PORT}
      - EMAIL_HOST_USER=\${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=\${EMAIL_HOST_PASSWORD}
      - EMAIL_USE_TLS=\${EMAIL_USE_TLS}
      - DEFAULT_FROM_EMAIL=\${DEFAULT_FROM_EMAIL}
      - CELERY_BROKER_URL=redis://:\${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:\${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - media_files:/app/media
      - static_files:/app/staticfiles
      - ./logs:/app/logs
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "/app/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker
  celery-worker:
    image: $BACKEND_IMAGE:$IMAGE_TAG
    restart: unless-stopped
    command: celery -A event_management worker --loglevel=info --concurrency=4
    environment:
      - SECRET_KEY=\${SECRET_KEY}
      - DEBUG=\${DEBUG:-False}
      - DATABASE_NAME=\${POSTGRES_DB}
      - DATABASE_USER=\${POSTGRES_USER}
      - DATABASE_PASSWORD=\${POSTGRES_PASSWORD}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:\${REDIS_PASSWORD}@redis:6379/0
      - CELERY_BROKER_URL=redis://:\${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:\${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - media_files:/app/media
      - ./logs:/app/logs
    depends_on:
      - db
      - redis

  # Celery Beat (Scheduler)
  celery-beat:
    image: $BACKEND_IMAGE:$IMAGE_TAG
    restart: unless-stopped
    command: celery -A event_management beat --loglevel=info
    environment:
      - SECRET_KEY=\${SECRET_KEY}
      - DEBUG=\${DEBUG:-False}
      - DATABASE_NAME=\${POSTGRES_DB}
      - DATABASE_USER=\${POSTGRES_USER}
      - DATABASE_PASSWORD=\${POSTGRES_PASSWORD}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://:\${REDIS_PASSWORD}@redis:6379/0
      - CELERY_BROKER_URL=redis://:\${REDIS_PASSWORD}@redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:\${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - ./logs:/app/logs
    depends_on:
      - db
      - redis

  # React Frontend
  frontend:
    image: $FRONTEND_IMAGE:$IMAGE_TAG
    restart: unless-stopped
    environment:
      - REACT_APP_API_BASE_URL=\${REACT_APP_API_BASE_URL}
      - REACT_APP_BACKEND_URL=\${REACT_APP_BACKEND_URL}
    depends_on:
      - backend

  # Nginx Reverse Proxy
  nginx:
    image: $NGINX_IMAGE:$IMAGE_TAG
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - static_files:/app/staticfiles:ro
      - media_files:/app/media:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
      - frontend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  media_files:
  static_files:

networks:
  default:
    driver: bridge
EOF

echo -e "${GREEN}✅ docker-compose.hub.yml created successfully${NC}"
echo ""
echo -e "${BLUE}🚀 To deploy using Docker Hub images:${NC}"
echo -e "${YELLOW}1. Copy .env.prod to your server${NC}"
echo -e "${YELLOW}2. Copy docker-compose.hub.yml to your server${NC}"
echo -e "${YELLOW}3. Run: docker-compose -f docker-compose.hub.yml up -d${NC}"
echo ""
echo -e "${GREEN}🎉 Build and push completed successfully!${NC}"

#!/bin/sh
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting University of Gondar Event Management System - Frontend${NC}"

# Replace environment variables in built files if needed
if [ -n "$REACT_APP_API_BASE_URL" ]; then
    echo -e "${YELLOW}Configuring API base URL: $REACT_APP_API_BASE_URL${NC}"
    find /usr/share/nginx/html -name "*.js" -exec sed -i "s|REACT_APP_API_BASE_URL_PLACEHOLDER|$REACT_APP_API_BASE_URL|g" {} \;
fi

if [ -n "$REACT_APP_BACKEND_URL" ]; then
    echo -e "${YELLOW}Configuring backend URL: $REACT_APP_BACKEND_URL${NC}"
    find /usr/share/nginx/html -name "*.js" -exec sed -i "s|REACT_APP_BACKEND_URL_PLACEHOLDER|$REACT_APP_BACKEND_URL|g" {} \;
fi

echo -e "${GREEN}Frontend initialization complete!${NC}"

# Execute the main command
exec "$@"

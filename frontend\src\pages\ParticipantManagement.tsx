import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Badge, Form, InputGroup, Alert, Spinner, Button, Modal, Table, Dropdown, ButtonGroup, Pagination } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { participantService, hotelService, driverService, Participant, Hotel, Driver } from '../services/api';

const ParticipantManagement: React.FC = () => {
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  
  // Modal states
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showBadgeModal, setShowBadgeModal] = useState(false);
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [selectedParticipant, setSelectedParticipant] = useState<Participant | null>(null);
  const [assignmentType, setAssignmentType] = useState<'hotel' | 'driver'>('hotel');
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [selectedDriver, setSelectedDriver] = useState<string>('');
  const [assignmentLoading, setAssignmentLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [badgeLoading, setBadgeLoading] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [participantsResponse, hotelsResponse, driversResponse] = await Promise.all([
        participantService.getParticipants(),
        hotelService.getHotels(),
        driverService.getDrivers(),
      ]);

      // Handle paginated responses
      const participantsData = Array.isArray(participantsResponse.data) 
        ? participantsResponse.data 
        : (participantsResponse.data as any).results || [];
      const hotelsData = Array.isArray(hotelsResponse.data) 
        ? hotelsResponse.data 
        : (hotelsResponse.data as any).results || [];
      const driversData = Array.isArray(driversResponse.data) 
        ? driversResponse.data 
        : (driversResponse.data as any).results || [];

      setParticipants(participantsData);
      setHotels(hotelsData.filter((hotel: Hotel) => hotel.is_active));
      setDrivers(driversData.filter((driver: Driver) => driver.is_available));
      setError(null);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to load data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filteredParticipants = participants.filter(participant => {
    const matchesSearch =
      participant.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      participant.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      participant.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      participant.institution_name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === '' ||
      (filterStatus === 'confirmed' && participant.is_confirmed) ||
      (filterStatus === 'pending' && !participant.is_confirmed) ||
      (filterStatus === 'assigned_hotel' && participant.assigned_hotel) ||
      (filterStatus === 'assigned_driver' && participant.assigned_driver);

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredParticipants.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentParticipants = filteredParticipants.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterStatus]);

  const handleConfirmParticipant = async (participantId: number) => {
    try {
      await participantService.confirmParticipant(participantId);
      await fetchData(); // Refresh data
    } catch (err) {
      console.error('Error confirming participant:', err);
      setError('Failed to confirm participant. Please try again.');
    }
  };

  const handleAssignment = async () => {
    if (!selectedParticipant) return;

    try {
      setAssignmentLoading(true);
      setError(null); // Clear any previous errors

      if (assignmentType === 'hotel') {
        if (selectedHotel) {
          await participantService.assignHotel(selectedParticipant.id, parseInt(selectedHotel));
        } else {
          // Remove assignment by passing 0
          await participantService.assignHotel(selectedParticipant.id, 0);
        }
      } else if (assignmentType === 'driver') {
        if (selectedDriver) {
          await participantService.assignDriver(selectedParticipant.id, parseInt(selectedDriver));
        } else {
          // Remove assignment by passing 0
          await participantService.assignDriver(selectedParticipant.id, 0);
        }
      }

      await fetchData(); // Refresh data
      setShowAssignModal(false);
      setSelectedParticipant(null);
      setSelectedHotel('');
      setSelectedDriver('');
    } catch (err) {
      console.error('Error updating assignment:', err);
      setError(`Failed to update ${assignmentType} assignment. Please try again.`);
    } finally {
      setAssignmentLoading(false);
    }
  };

  const handleGenerateBadge = async (participantId: number) => {
    try {
      setBadgeLoading(true);
      await participantService.regenerateBadge(participantId);
      await fetchData(); // Refresh data
      setError(null);
      alert('Badge generated successfully!');
    } catch (err) {
      console.error('Error generating badge:', err);
      setError('Failed to generate badge. Please try again.');
    } finally {
      setBadgeLoading(false);
    }
  };

  const handleDeleteParticipant = async () => {
    if (!selectedParticipant) return;

    try {
      setDeleteLoading(true);
      await participantService.deleteParticipant(selectedParticipant.id);
      await fetchData(); // Refresh data
      setShowDeleteModal(false);
      setSelectedParticipant(null);
      setError(null);
    } catch (err) {
      console.error('Error deleting participant:', err);
      setError('Failed to delete participant. Please try again.');
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleViewBadge = async (participantId: number) => {
    try {
      const response = await participantService.getParticipantBadge(participantId);
      // Open badge in new window
      window.open(response.data.badge_url, '_blank');
    } catch (err) {
      console.error('Error viewing badge:', err);
      setError('Failed to view badge. Please try again.');
    }
  };

  const openAssignModal = (participant: Participant, type: 'hotel' | 'driver') => {
    setSelectedParticipant(participant);
    setAssignmentType(type);
    // Pre-select current assignment if exists
    if (type === 'hotel' && participant.assigned_hotel) {
      setSelectedHotel(participant.assigned_hotel.toString());
    } else if (type === 'driver' && participant.assigned_driver) {
      setSelectedDriver(participant.assigned_driver.toString());
    }
    setShowAssignModal(true);
  };

  const openViewModal = (participant: Participant) => {
    setSelectedParticipant(participant);
    setShowViewModal(true);
  };

  const openDeleteModal = (participant: Participant) => {
    setSelectedParticipant(participant);
    setShowDeleteModal(true);
  };

  const openBadgeModal = (participant: Participant) => {
    setSelectedParticipant(participant);
    setShowBadgeModal(true);
  };

  const openAssignmentModal = (participant: Participant) => {
    setSelectedParticipant(participant);
    // Pre-select current assignments
    if (participant.assigned_hotel) {
      setSelectedHotel(participant.assigned_hotel.toString());
    } else {
      setSelectedHotel('');
    }
    if (participant.assigned_driver) {
      setSelectedDriver(participant.assigned_driver.toString());
    } else {
      setSelectedDriver('');
    }
    setShowAssignmentModal(true);
  };

  const getStatusBadge = (participant: Participant) => {
    if (!participant.is_confirmed) {
      return <Badge bg="warning">Pending Approval</Badge>;
    }
    if (participant.assigned_hotel && participant.assigned_driver) {
      return <Badge bg="success">Fully Assigned</Badge>;
    }
    if (participant.assigned_hotel || participant.assigned_driver) {
      return <Badge bg="info">Partially Assigned</Badge>;
    }
    return <Badge bg="secondary">Confirmed</Badge>;
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading participants...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h1 className="display-5 fw-bold text-primary">
                <i className="fas fa-users-cog me-3"></i>
                Participant Management
              </h1>
              <p className="lead text-muted">Approve registrations and assign accommodations & transportation</p>
            </div>
          </div>

          {/* Search and Filter Controls */}
          <Row className="mb-4">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="fas fa-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search participants by name, email, or institution..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6}>
              <Form.Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="">All Participants</option>
                <option value="pending">Pending Approval</option>
                <option value="confirmed">Confirmed</option>
                <option value="assigned_hotel">Assigned Hotel</option>
                <option value="assigned_driver">Assigned Driver</option>
              </Form.Select>
            </Col>
          </Row>

          {error && (
            <Alert variant="danger" className="mb-4">
              <i className="fas fa-exclamation-triangle me-2"></i>
              {error}
            </Alert>
          )}

          {/* Participants Table */}
          <Card className="shadow-sm">
            <Card.Header className="bg-primary text-white">
              <h5 className="mb-0">
                <i className="fas fa-list me-2"></i>
                Registered Participants ({filteredParticipants.length})
              </h5>
            </Card.Header>
            <Card.Body className="p-0">
              {currentParticipants.length === 0 ? (
                <div className="text-center py-5">
                  <i className="fas fa-users fa-3x text-muted mb-3"></i>
                  <h5 className="text-muted">No participants found</h5>
                  <p className="text-muted">Try adjusting your search or filter criteria.</p>
                </div>
              ) : (
                <div className="table-responsive">
                  <Table hover className="mb-0">
                    <thead className="table-light">
                      <tr>
                        <th>Participant</th>
                        <th>Contact</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Assignments</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {currentParticipants.map((participant) => (
                        <tr key={participant.id}>
                          <td>
                            <div>
                              <strong>{participant.full_name}</strong>
                              <br />
                              <small className="text-muted">{participant.institution_name}</small>
                            </div>
                          </td>
                          <td>
                            <div>
                              <small>{participant.email}</small>
                              <br />
                              <small className="text-muted">{participant.phone}</small>
                            </div>
                          </td>
                          <td>
                            <Badge 
                              bg="secondary" 
                              style={{ backgroundColor: participant.participant_type_color }}
                            >
                              {participant.participant_type_name}
                            </Badge>
                          </td>
                          <td>{getStatusBadge(participant)}</td>
                          <td>
                            <div className="d-flex flex-column gap-1">
                              <div className="d-flex gap-1">
                                {participant.assigned_hotel ? (
                                  <Badge bg="success" className="d-flex align-items-center">
                                    <i className="fas fa-hotel me-1"></i>
                                    Hotel
                                  </Badge>
                                ) : (
                                  <Badge bg="secondary" className="d-flex align-items-center">
                                    <i className="fas fa-hotel me-1"></i>
                                    No Hotel
                                  </Badge>
                                )}
                                {participant.assigned_driver ? (
                                  <Badge bg="success" className="d-flex align-items-center">
                                    <i className="fas fa-car me-1"></i>
                                    Driver
                                  </Badge>
                                ) : (
                                  <Badge bg="secondary" className="d-flex align-items-center">
                                    <i className="fas fa-car me-1"></i>
                                    No Driver
                                  </Badge>
                                )}
                              </div>
                              <Button
                                size="sm"
                                variant="outline-primary"
                                onClick={() => openAssignmentModal(participant)}
                                className="w-100"
                              >
                                <i className="fas fa-cog me-1"></i>
                                Manage
                              </Button>
                            </div>
                          </td>
                          <td>
                            <div className="d-flex gap-1 flex-wrap">
                              <Button
                                size="sm"
                                variant="outline-info"
                                onClick={() => openViewModal(participant)}
                                title="View Details"
                              >
                                <i className="fas fa-eye"></i>
                              </Button>

                              <Link to={`/participants/${participant.id}/edit`}>
                                <Button
                                  size="sm"
                                  variant="outline-primary"
                                  title="Edit Participant"
                                >
                                  <i className="fas fa-edit"></i>
                                </Button>
                              </Link>

                              {!participant.is_confirmed && (
                                <Button
                                  size="sm"
                                  variant="outline-success"
                                  onClick={() => handleConfirmParticipant(participant.id)}
                                  title="Confirm Registration"
                                >
                                  <i className="fas fa-check"></i>
                                </Button>
                              )}

                              <Button
                                size="sm"
                                variant="outline-warning"
                                onClick={() => openBadgeModal(participant)}
                                title="Manage Badge"
                              >
                                <i className="fas fa-id-badge"></i>
                              </Button>

                              <Button
                                size="sm"
                                variant="outline-danger"
                                onClick={() => openDeleteModal(participant)}
                                title="Delete Participant"
                              >
                                <i className="fas fa-trash"></i>
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              )}
            </Card.Body>

            {/* Pagination */}
            {totalPages > 1 && (
              <Card.Footer className="d-flex justify-content-between align-items-center">
                <div className="text-muted">
                  Showing {startIndex + 1} to {Math.min(endIndex, filteredParticipants.length)} of {filteredParticipants.length} participants
                </div>
                <Pagination className="mb-0">
                  <Pagination.First
                    onClick={() => setCurrentPage(1)}
                    disabled={currentPage === 1}
                  />
                  <Pagination.Prev
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  />

                  {[...Array(totalPages)].map((_, index) => {
                    const page = index + 1;
                    if (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 2 && page <= currentPage + 2)
                    ) {
                      return (
                        <Pagination.Item
                          key={page}
                          active={page === currentPage}
                          onClick={() => setCurrentPage(page)}
                        >
                          {page}
                        </Pagination.Item>
                      );
                    } else if (
                      page === currentPage - 3 ||
                      page === currentPage + 3
                    ) {
                      return <Pagination.Ellipsis key={page} />;
                    }
                    return null;
                  })}

                  <Pagination.Next
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  />
                  <Pagination.Last
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={currentPage === totalPages}
                  />
                </Pagination>
              </Card.Footer>
            )}
          </Card>
        </Col>
      </Row>

      {/* Assignment Modal */}
      <Modal show={showAssignModal} onHide={() => setShowAssignModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className={`fas fa-${assignmentType === 'hotel' ? 'hotel' : 'car'} me-2`}></i>
            Assign {assignmentType === 'hotel' ? 'Hotel' : 'Driver'} to {selectedParticipant?.full_name}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedParticipant && (
            <div className="mb-3">
              <Alert variant="info">
                <strong>Current Assignment:</strong> {
                  assignmentType === 'hotel'
                    ? (selectedParticipant.assigned_hotel
                        ? `Hotel assigned (ID: ${selectedParticipant.assigned_hotel})`
                        : 'No hotel assigned')
                    : (selectedParticipant.assigned_driver
                        ? `Driver assigned (ID: ${selectedParticipant.assigned_driver})`
                        : 'No driver assigned')
                }
              </Alert>
            </div>
          )}

          {assignmentType === 'hotel' ? (
            <Form.Group>
              <Form.Label className="fw-semibold">
                <i className="fas fa-hotel me-2"></i>
                Select Hotel
              </Form.Label>
              <Form.Select
                value={selectedHotel}
                onChange={(e) => setSelectedHotel(e.target.value)}
                className="form-control-lg"
              >
                <option value="">Remove hotel assignment</option>
                {hotels.map((hotel) => (
                  <option key={hotel.id} value={hotel.id}>
                    {hotel.name} - {hotel.address} ({hotel.available_rooms_count} rooms available)
                  </option>
                ))}
              </Form.Select>
              <Form.Text className="text-muted">
                Select "Remove hotel assignment" to unassign the current hotel
              </Form.Text>
            </Form.Group>
          ) : (
            <Form.Group>
              <Form.Label className="fw-semibold">
                <i className="fas fa-car me-2"></i>
                Select Driver
              </Form.Label>
              <Form.Select
                value={selectedDriver}
                onChange={(e) => setSelectedDriver(e.target.value)}
                className="form-control-lg"
              >
                <option value="">Remove driver assignment</option>
                {drivers.map((driver) => (
                  <option key={driver.id} value={driver.id}>
                    {driver.name} - {driver.car_model} ({driver.car_plate})
                  </option>
                ))}
              </Form.Select>
              <Form.Text className="text-muted">
                Select "Remove driver assignment" to unassign the current driver
              </Form.Text>
            </Form.Group>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAssignModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleAssignment}
            disabled={assignmentLoading}
          >
            {assignmentLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Updating...
              </>
            ) : (
              <>
                <i className="fas fa-save me-2"></i>
                Update Assignment
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* View Participant Modal */}
      <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size="xl">
        <Modal.Header closeButton className="bg-primary text-white">
          <Modal.Title>
            <i className="fas fa-user-circle me-2"></i>
            Participant Profile
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-4">
          {selectedParticipant && (
            <div>
              {/* Header Section with Photo and Basic Info */}
              <Row className="mb-4">
                <Col md={3} className="text-center">
                  <div className="mb-3">
                    {selectedParticipant.profile_photo ? (
                      <img
                        src={selectedParticipant.profile_photo}
                        alt="Profile"
                        className="rounded-circle border border-3 border-primary"
                        style={{ width: '120px', height: '120px', objectFit: 'cover' }}
                      />
                    ) : (
                      <div
                        className="rounded-circle border border-3 border-secondary d-flex align-items-center justify-content-center"
                        style={{ width: '120px', height: '120px', backgroundColor: '#f8f9fa' }}
                      >
                        <i className="fas fa-user fa-3x text-muted"></i>
                      </div>
                    )}
                  </div>
                  <h5 className="fw-bold">{selectedParticipant.full_name}</h5>
                  <Badge
                    bg="primary"
                    style={{ backgroundColor: selectedParticipant.participant_type_color }}
                    className="mb-2"
                  >
                    {selectedParticipant.participant_type_name}
                  </Badge>
                  <br />
                  {getStatusBadge(selectedParticipant)}
                </Col>
                <Col md={9}>
                  <Row>
                    <Col md={6}>
                      <Card className="h-100 border-0 bg-light">
                        <Card.Body>
                          <h6 className="text-primary mb-3">
                            <i className="fas fa-user me-2"></i>
                            Personal Information
                          </h6>
                          <div className="mb-2">
                            <strong>Email:</strong>
                            <br />
                            <a href={`mailto:${selectedParticipant.email}`} className="text-decoration-none">
                              {selectedParticipant.email}
                            </a>
                          </div>
                          <div className="mb-2">
                            <strong>Phone:</strong>
                            <br />
                            <a href={`tel:${selectedParticipant.phone}`} className="text-decoration-none">
                              {selectedParticipant.phone}
                            </a>
                          </div>
                          <div className="mb-2">
                            <strong>Institution:</strong>
                            <br />
                            {selectedParticipant.institution_name}
                          </div>
                          <div>
                            <strong>Position:</strong>
                            <br />
                            {selectedParticipant.position}
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                    <Col md={6}>
                      <Card className="h-100 border-0 bg-light">
                        <Card.Body>
                          <h6 className="text-success mb-3">
                            <i className="fas fa-calendar me-2"></i>
                            Event Information
                          </h6>
                          <div className="mb-2">
                            <strong>Event:</strong>
                            <br />
                            {selectedParticipant.event_name}
                          </div>
                          <div className="mb-2">
                            <strong>Arrival:</strong>
                            <br />
                            <i className="fas fa-plane-arrival me-1 text-primary"></i>
                            {new Date(selectedParticipant.arrival_date).toLocaleString()}
                          </div>
                          <div className="mb-2">
                            <strong>Departure:</strong>
                            <br />
                            <i className="fas fa-plane-departure me-1 text-primary"></i>
                            {new Date(selectedParticipant.departure_date).toLocaleString()}
                          </div>
                          <div>
                            <strong>Registration Date:</strong>
                            <br />
                            {new Date(selectedParticipant.registration_date).toLocaleDateString()}
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                  </Row>
                </Col>
              </Row>

              {/* Assignments Section */}
              <Row className="mb-4">
                <Col md={6}>
                  <Card className="border-primary">
                    <Card.Header className="bg-primary text-white">
                      <h6 className="mb-0">
                        <i className="fas fa-hotel me-2"></i>
                        Hotel Assignment
                      </h6>
                    </Card.Header>
                    <Card.Body>
                      {selectedParticipant.assigned_hotel ? (
                        <div>
                          <Badge bg="success" className="mb-3">
                            <i className="fas fa-check me-1"></i>
                            Assigned
                          </Badge>
                          <div className="mb-3">
                            <h6 className="mb-2 text-primary">{selectedParticipant.assigned_hotel_name || 'Hotel Name Not Available'}</h6>

                            {selectedParticipant.assigned_hotel_address && (
                              <div className="mb-2">
                                <i className="fas fa-map-marker-alt me-2 text-muted"></i>
                                <small className="text-muted">{selectedParticipant.assigned_hotel_address}</small>
                              </div>
                            )}

                            {selectedParticipant.assigned_hotel_phone && (
                              <div className="mb-2">
                                <i className="fas fa-phone me-2 text-muted"></i>
                                <small>
                                  <a href={`tel:${selectedParticipant.assigned_hotel_phone}`} className="text-decoration-none">
                                    {selectedParticipant.assigned_hotel_phone}
                                  </a>
                                </small>
                              </div>
                            )}

                            {selectedParticipant.assigned_hotel_email && (
                              <div className="mb-3">
                                <i className="fas fa-envelope me-2 text-muted"></i>
                                <small>
                                  <a href={`mailto:${selectedParticipant.assigned_hotel_email}`} className="text-decoration-none">
                                    {selectedParticipant.assigned_hotel_email}
                                  </a>
                                </small>
                              </div>
                            )}

                            <div className="d-flex gap-2">
                              <Link to={`/hotels/${selectedParticipant.assigned_hotel}`}>
                                <Button variant="outline-primary" size="sm">
                                  <i className="fas fa-eye me-1"></i>
                                  View Details
                                </Button>
                              </Link>
                              <Button
                                variant="outline-secondary"
                                size="sm"
                                onClick={() => openAssignmentModal(selectedParticipant)}
                              >
                                <i className="fas fa-edit me-1"></i>
                                Change
                              </Button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <Badge bg="secondary" className="mb-2">
                            <i className="fas fa-times me-1"></i>
                            Not Assigned
                          </Badge>
                          <p className="mb-0 text-muted">No hotel has been assigned yet</p>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            className="mt-2"
                            onClick={() => openAssignmentModal(selectedParticipant)}
                          >
                            <i className="fas fa-plus me-1"></i>
                            Assign Hotel
                          </Button>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
                <Col md={6}>
                  <Card className="border-success">
                    <Card.Header className="bg-success text-white">
                      <h6 className="mb-0">
                        <i className="fas fa-car me-2"></i>
                        Driver Assignment
                      </h6>
                    </Card.Header>
                    <Card.Body>
                      {selectedParticipant.assigned_driver ? (
                        <div>
                          <Badge bg="success" className="mb-3">
                            <i className="fas fa-check me-1"></i>
                            Assigned
                          </Badge>
                          <div className="mb-3">
                            <h6 className="mb-2 text-success">{selectedParticipant.assigned_driver_name || 'Driver Name Not Available'}</h6>

                            {selectedParticipant.assigned_driver_phone && (
                              <div className="mb-2">
                                <i className="fas fa-phone me-2 text-muted"></i>
                                <small>
                                  <a href={`tel:${selectedParticipant.assigned_driver_phone}`} className="text-decoration-none">
                                    {selectedParticipant.assigned_driver_phone}
                                  </a>
                                </small>
                              </div>
                            )}

                            {selectedParticipant.assigned_driver_car_model && (
                              <div className="mb-2">
                                <i className="fas fa-car me-2 text-muted"></i>
                                <small className="text-muted">
                                  {selectedParticipant.assigned_driver_car_model}
                                  {selectedParticipant.assigned_driver_car_plate && (
                                    <span className="ms-2">
                                      <Badge bg="secondary" className="ms-1">
                                        {selectedParticipant.assigned_driver_car_plate}
                                      </Badge>
                                    </span>
                                  )}
                                </small>
                              </div>
                            )}

                            <div className="d-flex gap-2">
                              <Link to={`/drivers/${selectedParticipant.assigned_driver}`}>
                                <Button variant="outline-success" size="sm">
                                  <i className="fas fa-eye me-1"></i>
                                  View Details
                                </Button>
                              </Link>
                              <Button
                                variant="outline-secondary"
                                size="sm"
                                onClick={() => openAssignmentModal(selectedParticipant)}
                              >
                                <i className="fas fa-edit me-1"></i>
                                Change
                              </Button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <Badge bg="secondary" className="mb-2">
                            <i className="fas fa-times me-1"></i>
                            Not Assigned
                          </Badge>
                          <p className="mb-0 text-muted">No driver has been assigned yet</p>
                          <Button
                            variant="outline-success"
                            size="sm"
                            className="mt-2"
                            onClick={() => openAssignmentModal(selectedParticipant)}
                          >
                            <i className="fas fa-plus me-1"></i>
                            Assign Driver
                          </Button>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
              </Row>

              {/* Badge Section */}
              <Row className="mb-4">
                <Col>
                  <Card className="border-warning">
                    <Card.Header className="bg-warning text-dark">
                      <h6 className="mb-0">
                        <i className="fas fa-id-badge me-2"></i>
                        Badge Information
                      </h6>
                    </Card.Header>
                    <Card.Body>
                      <Row>
                        <Col md={8}>
                          {selectedParticipant.badge_generated ? (
                            <div>
                              <Badge bg="success" className="mb-2">
                                <i className="fas fa-check me-1"></i>
                                Badge Generated
                              </Badge>
                              <p className="mb-0">Badge has been successfully generated and is ready for use.</p>
                              {selectedParticipant.badge_url && (
                                <Button
                                  variant="outline-primary"
                                  size="sm"
                                  className="mt-2"
                                  onClick={() => handleViewBadge(selectedParticipant.id)}
                                >
                                  <i className="fas fa-download me-1"></i>
                                  Download Badge
                                </Button>
                              )}
                            </div>
                          ) : (
                            <div>
                              <Badge bg="secondary" className="mb-2">
                                <i className="fas fa-times me-1"></i>
                                Badge Not Generated
                              </Badge>
                              <p className="mb-0">Badge has not been generated yet.</p>
                              <Button
                                variant="outline-warning"
                                size="sm"
                                className="mt-2"
                                onClick={() => handleGenerateBadge(selectedParticipant.id)}
                              >
                                <i className="fas fa-id-badge me-1"></i>
                                Generate Badge
                              </Button>
                            </div>
                          )}
                        </Col>
                        <Col md={4} className="text-center">
                          {selectedParticipant.badge_url && (
                            <img
                              src={selectedParticipant.badge_url}
                              alt="Badge Preview"
                              className="img-fluid border rounded"
                              style={{ maxHeight: '100px' }}
                            />
                          )}
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>

              {/* Remarks Section */}
              {selectedParticipant.remarks && (
                <Row>
                  <Col>
                    <Card className="border-info">
                      <Card.Header className="bg-info text-white">
                        <h6 className="mb-0">
                          <i className="fas fa-comment me-2"></i>
                          Special Requirements / Remarks
                        </h6>
                      </Card.Header>
                      <Card.Body>
                        <p className="mb-0">{selectedParticipant.remarks}</p>
                      </Card.Body>
                    </Card>
                  </Col>
                </Row>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="bg-light">
          <Button variant="secondary" onClick={() => setShowViewModal(false)}>
            <i className="fas fa-times me-2"></i>
            Close
          </Button>
          {selectedParticipant && (
            <>
              <Button
                variant="warning"
                onClick={() => openAssignmentModal(selectedParticipant)}
              >
                <i className="fas fa-cog me-2"></i>
                Manage Assignments
              </Button>
              <Link to={`/participants/${selectedParticipant.id}/edit`}>
                <Button variant="primary">
                  <i className="fas fa-edit me-2"></i>
                  Edit Participant
                </Button>
              </Link>
            </>
          )}
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">
            <i className="fas fa-exclamation-triangle me-2"></i>
            Confirm Deletion
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Are you sure you want to delete <strong>{selectedParticipant?.full_name}</strong>?</p>
          <Alert variant="warning">
            <i className="fas fa-warning me-2"></i>
            This action cannot be undone. All participant data, including badge information, will be permanently deleted.
          </Alert>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteParticipant}
            disabled={deleteLoading}
          >
            {deleteLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Deleting...
              </>
            ) : (
              <>
                <i className="fas fa-trash me-2"></i>
                Delete Participant
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Badge Management Modal */}
      <Modal show={showBadgeModal} onHide={() => setShowBadgeModal(false)} size="lg">
        <Modal.Header closeButton className="bg-warning text-dark">
          <Modal.Title>
            <i className="fas fa-id-badge me-2"></i>
            Badge Management - {selectedParticipant?.full_name}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-4">
          {selectedParticipant && (
            <div>
              {/* Participant Info Summary */}
              <Row className="mb-4">
                <Col>
                  <Card className="bg-light border-0">
                    <Card.Body className="py-3">
                      <Row className="align-items-center">
                        <Col md={3} className="text-center">
                          {selectedParticipant.profile_photo ? (
                            <img
                              src={selectedParticipant.profile_photo}
                              alt="Profile"
                              className="rounded-circle border border-2 border-primary"
                              style={{ width: '80px', height: '80px', objectFit: 'cover' }}
                            />
                          ) : (
                            <div
                              className="rounded-circle border border-2 border-secondary d-flex align-items-center justify-content-center mx-auto"
                              style={{ width: '80px', height: '80px', backgroundColor: '#f8f9fa' }}
                            >
                              <i className="fas fa-user fa-2x text-muted"></i>
                            </div>
                          )}
                        </Col>
                        <Col md={9}>
                          <h5 className="mb-1">{selectedParticipant.full_name}</h5>
                          <p className="mb-1 text-muted">{selectedParticipant.institution_name}</p>
                          <Badge
                            bg="primary"
                            style={{ backgroundColor: selectedParticipant.participant_type_color }}
                          >
                            {selectedParticipant.participant_type_name}
                          </Badge>
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>

              {/* Badge Status and Actions */}
              <Row>
                <Col md={8}>
                  {selectedParticipant.badge_generated && selectedParticipant.badge_url ? (
                    <div>
                      <Alert variant="success" className="d-flex align-items-center">
                        <i className="fas fa-check-circle fa-2x me-3"></i>
                        <div>
                          <h6 className="mb-1">Badge Successfully Generated</h6>
                          <p className="mb-0">The participant badge is ready for printing and distribution.</p>
                        </div>
                      </Alert>

                      <div className="d-grid gap-2">
                        <Button
                          variant="primary"
                          size="lg"
                          onClick={() => handleViewBadge(selectedParticipant.id)}
                        >
                          <i className="fas fa-download me-2"></i>
                          Download Badge (High Quality)
                        </Button>

                        <Button
                          variant="outline-warning"
                          onClick={() => handleGenerateBadge(selectedParticipant.id)}
                          disabled={badgeLoading}
                        >
                          {badgeLoading ? (
                            <>
                              <Spinner animation="border" size="sm" className="me-2" />
                              Regenerating Badge...
                            </>
                          ) : (
                            <>
                              <i className="fas fa-sync me-2"></i>
                              Regenerate Badge
                            </>
                          )}
                        </Button>
                      </div>

                      <div className="mt-3">
                        <small className="text-muted">
                          <i className="fas fa-info-circle me-1"></i>
                          Badge was generated on {new Date(selectedParticipant.registration_date).toLocaleDateString()}
                        </small>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <Alert variant="warning" className="d-flex align-items-center">
                        <i className="fas fa-exclamation-triangle fa-2x me-3"></i>
                        <div>
                          <h6 className="mb-1">Badge Not Generated</h6>
                          <p className="mb-0">This participant doesn't have a badge yet. Generate one now to enable event access.</p>
                        </div>
                      </Alert>

                      <div className="d-grid">
                        <Button
                          variant="success"
                          size="lg"
                          onClick={() => handleGenerateBadge(selectedParticipant.id)}
                          disabled={badgeLoading}
                        >
                          {badgeLoading ? (
                            <>
                              <Spinner animation="border" size="sm" className="me-2" />
                              Generating Badge...
                            </>
                          ) : (
                            <>
                              <i className="fas fa-id-badge me-2"></i>
                              Generate Badge Now
                            </>
                          )}
                        </Button>
                      </div>

                      <div className="mt-3">
                        <small className="text-muted">
                          <i className="fas fa-info-circle me-1"></i>
                          Badge generation requires a profile photo. Make sure the participant has uploaded their photo.
                        </small>
                      </div>
                    </div>
                  )}
                </Col>

                <Col md={4} className="text-center">
                  <div className="border rounded p-3 bg-light">
                    <h6 className="text-muted mb-3">Badge Preview</h6>
                    {selectedParticipant.badge_url ? (
                      <div>
                        <img
                          src={selectedParticipant.badge_url}
                          alt="Badge Preview"
                          className="img-fluid border rounded shadow-sm"
                          style={{ maxHeight: '200px', cursor: 'pointer' }}
                          onClick={() => handleViewBadge(selectedParticipant.id)}
                        />
                        <div className="mt-2">
                          <small className="text-muted">Click to view full size</small>
                        </div>
                      </div>
                    ) : (
                      <div
                        className="d-flex align-items-center justify-content-center border-2 border-dashed rounded"
                        style={{ height: '200px', borderColor: '#dee2e6' }}
                      >
                        <div className="text-center">
                          <i className="fas fa-id-badge fa-3x text-muted mb-2"></i>
                          <p className="text-muted mb-0">No badge generated</p>
                        </div>
                      </div>
                    )}
                  </div>
                </Col>
              </Row>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="bg-light">
          <Button variant="secondary" onClick={() => setShowBadgeModal(false)}>
            <i className="fas fa-times me-2"></i>
            Close
          </Button>
          {selectedParticipant?.badge_url && (
            <Button
              variant="info"
              onClick={() => {
                // Print functionality
                const printWindow = window.open(selectedParticipant.badge_url, '_blank');
                if (printWindow) {
                  printWindow.onload = () => {
                    printWindow.print();
                  };
                }
              }}
            >
              <i className="fas fa-print me-2"></i>
              Print Badge
            </Button>
          )}
        </Modal.Footer>
      </Modal>

      {/* Comprehensive Assignment Modal */}
      <Modal show={showAssignmentModal} onHide={() => setShowAssignmentModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-cog me-2"></i>
            Manage Assignments - {selectedParticipant?.full_name}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedParticipant && (
            <Row>
              {/* Hotel Assignment */}
              <Col md={6}>
                <Card className="h-100">
                  <Card.Header className="bg-primary text-white">
                    <h6 className="mb-0">
                      <i className="fas fa-hotel me-2"></i>
                      Hotel Assignment
                    </h6>
                  </Card.Header>
                  <Card.Body>
                    <div className="mb-3">
                      <strong>Current Status:</strong>
                      <br />
                      {selectedParticipant.assigned_hotel ? (
                        <Badge bg="success" className="mt-1">
                          <i className="fas fa-check me-1"></i>
                          Hotel Assigned (ID: {selectedParticipant.assigned_hotel})
                        </Badge>
                      ) : (
                        <Badge bg="secondary" className="mt-1">
                          <i className="fas fa-times me-1"></i>
                          No Hotel Assigned
                        </Badge>
                      )}
                    </div>

                    <Form.Group>
                      <Form.Label className="fw-semibold">Select Hotel</Form.Label>
                      <Form.Select
                        value={selectedHotel}
                        onChange={(e) => setSelectedHotel(e.target.value)}
                        className="form-control-lg"
                      >
                        <option value="">Remove hotel assignment</option>
                        {hotels.map((hotel) => (
                          <option key={hotel.id} value={hotel.id}>
                            {hotel.name} - {hotel.address}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Card.Body>
                </Card>
              </Col>

              {/* Driver Assignment */}
              <Col md={6}>
                <Card className="h-100">
                  <Card.Header className="bg-success text-white">
                    <h6 className="mb-0">
                      <i className="fas fa-car me-2"></i>
                      Driver Assignment
                    </h6>
                  </Card.Header>
                  <Card.Body>
                    <div className="mb-3">
                      <strong>Current Status:</strong>
                      <br />
                      {selectedParticipant.assigned_driver ? (
                        <Badge bg="success" className="mt-1">
                          <i className="fas fa-check me-1"></i>
                          Driver Assigned (ID: {selectedParticipant.assigned_driver})
                        </Badge>
                      ) : (
                        <Badge bg="secondary" className="mt-1">
                          <i className="fas fa-times me-1"></i>
                          No Driver Assigned
                        </Badge>
                      )}
                    </div>

                    <Form.Group>
                      <Form.Label className="fw-semibold">Select Driver</Form.Label>
                      <Form.Select
                        value={selectedDriver}
                        onChange={(e) => setSelectedDriver(e.target.value)}
                        className="form-control-lg"
                      >
                        <option value="">Remove driver assignment</option>
                        {drivers.map((driver) => (
                          <option key={driver.id} value={driver.id}>
                            {driver.name} - {driver.car_model} ({driver.car_plate})
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAssignmentModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={async () => {
              if (!selectedParticipant) return;

              try {
                setAssignmentLoading(true);
                setError(null); // Clear any previous errors

                // Update hotel assignment
                try {
                  if (selectedHotel) {
                    await participantService.assignHotel(selectedParticipant.id, parseInt(selectedHotel));
                  } else {
                    await participantService.assignHotel(selectedParticipant.id, 0);
                  }
                } catch (hotelError) {
                  console.error('Hotel assignment error:', hotelError);
                  throw new Error('Failed to update hotel assignment');
                }

                // Update driver assignment
                try {
                  if (selectedDriver) {
                    await participantService.assignDriver(selectedParticipant.id, parseInt(selectedDriver));
                  } else {
                    await participantService.assignDriver(selectedParticipant.id, 0);
                  }
                } catch (driverError) {
                  console.error('Driver assignment error:', driverError);
                  throw new Error('Failed to update driver assignment');
                }

                await fetchData(); // Refresh data
                setShowAssignmentModal(false);
                setSelectedParticipant(null);
                setSelectedHotel('');
                setSelectedDriver('');
              } catch (err) {
                console.error('Error updating assignments:', err);
                setError(err instanceof Error ? err.message : 'Failed to update assignments. Please try again.');
              } finally {
                setAssignmentLoading(false);
              }
            }}
            disabled={assignmentLoading}
          >
            {assignmentLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Updating...
              </>
            ) : (
              <>
                <i className="fas fa-save me-2"></i>
                Update Assignments
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ParticipantManagement;

version: '3.8'

services:
  # PostgreSQL Database for Local Development
  db:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-uog_event_local}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
    volumes:
      - postgres_local_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Celery and Caching
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_local_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django Backend (Local Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: unless-stopped
    command: python manage.py runserver 0.0.0.0:8000
    environment:
      - SECRET_KEY=${SECRET_KEY:-django-insecure-local-development-key}
      - DEBUG=${DEBUG:-True}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1,0.0.0.0,backend}
      - DATABASE_NAME=${POSTGRES_DB:-uog_event_local}
      - DATABASE_USER=${POSTGRES_USER:-postgres}
      - DATABASE_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://redis:6379/0
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:3000,http://127.0.0.1:3000,http://localhost:3001}
      - MEDIA_URL=/media/
      - STATIC_URL=/static/
      - MEDIA_ROOT=/app/media
      - STATIC_ROOT=/app/staticfiles
      - QR_CODE_BASE_URL=${QR_CODE_BASE_URL:-http://localhost:8000}
      - EMAIL_HOST=${EMAIL_HOST:-smtp.office365.com}
      - EMAIL_PORT=${EMAIL_PORT:-587}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - EMAIL_USE_TLS=${EMAIL_USE_TLS:-True}
      - DEFAULT_FROM_EMAIL=${DEFAULT_FROM_EMAIL:-UoG Events Local <<EMAIL>>}
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - HEALTH_CHECK_URL=http://backend:8000/admin/login/
    volumes:
      - ./backend:/app
      - media_local_files:/app/media
      - static_local_files:/app/staticfiles
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/admin/login/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker (Local)
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: unless-stopped
    environment:
      - SECRET_KEY=${SECRET_KEY:-django-insecure-local-development-key}
      - DEBUG=${DEBUG:-True}
      - DATABASE_NAME=${POSTGRES_DB:-uog_event_local}
      - DATABASE_USER=${POSTGRES_USER:-postgres}
      - DATABASE_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - EMAIL_HOST=${EMAIL_HOST:-smtp.office365.com}
      - EMAIL_PORT=${EMAIL_PORT:-587}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - EMAIL_USE_TLS=${EMAIL_USE_TLS:-True}
      - DEFAULT_FROM_EMAIL=${DEFAULT_FROM_EMAIL:-UoG Events Local <<EMAIL>>}
    volumes:
      - ./backend:/app
      - media_local_files:/app/media
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    command: celery -A event_management worker --loglevel=info --concurrency=2

  # React Frontend (Local Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    restart: unless-stopped
    environment:
      - REACT_APP_API_BASE_URL=${REACT_APP_API_BASE_URL:-http://localhost:8000/api}
      - REACT_APP_BACKEND_URL=${REACT_APP_BACKEND_URL:-http://localhost:8000}
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3001:3000"
    depends_on:
      - backend
    stdin_open: true
    tty: true

volumes:
  postgres_local_data:
  redis_local_data:
  media_local_files:
  static_local_files:

networks:
  default:
    name: uog-event-local

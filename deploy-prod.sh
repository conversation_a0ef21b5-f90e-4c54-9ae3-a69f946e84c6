#!/bin/bash

# University of Gondar Event Management System - Production Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}UoG Event Management - Production Deployment${NC}"
echo -e "${BLUE}============================================${NC}"

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}Please run this script as root or with sudo${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Error: Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo -e "${RED}Error: Docker Compose is not installed.${NC}"
    exit 1
fi

# Load environment variables
if [ -f .env.prod ]; then
    echo -e "${YELLOW}Loading production environment variables...${NC}"
    export $(cat .env.prod | grep -v '^#' | xargs)
else
    echo -e "${RED}Error: .env.prod file not found. Please create it from .env.prod.template${NC}"
    exit 1
fi

# Validate required environment variables
required_vars=("SECRET_KEY" "POSTGRES_PASSWORD" "REDIS_PASSWORD" "EMAIL_HOST_PASSWORD")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${RED}Error: Required environment variable $var is not set${NC}"
        exit 1
    fi
done

# Create necessary directories
echo -e "${YELLOW}Creating necessary directories...${NC}"
mkdir -p logs/nginx
mkdir -p backups
mkdir -p nginx/ssl

# Check SSL certificates
if [ ! -f "nginx/ssl/cert.pem" ] || [ ! -f "nginx/ssl/private.key" ]; then
    echo -e "${YELLOW}SSL certificates not found. Please place your SSL certificates in nginx/ssl/${NC}"
    echo -e "${YELLOW}Required files: cert.pem and private.key${NC}"
    read -p "Continue without SSL? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Backup existing data (if any)
if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
    echo -e "${YELLOW}Creating backup of existing data...${NC}"
    timestamp=$(date +%Y%m%d_%H%M%S)
    docker-compose -f docker-compose.prod.yml exec -T db pg_dump -U $POSTGRES_USER $POSTGRES_DB > "backups/backup_${timestamp}.sql"
    echo -e "${GREEN}Backup created: backups/backup_${timestamp}.sql${NC}"
fi

# Stop existing containers
echo -e "${YELLOW}Stopping existing containers...${NC}"
docker-compose -f docker-compose.prod.yml down

# Pull latest images and build
echo -e "${YELLOW}Building production images...${NC}"
docker-compose -f docker-compose.prod.yml build --no-cache

# Start services
echo -e "${YELLOW}Starting production services...${NC}"
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be ready
echo -e "${YELLOW}Waiting for services to be ready...${NC}"
sleep 30

# Check service health
echo -e "${YELLOW}Checking service health...${NC}"

# Check database
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if docker-compose -f docker-compose.prod.yml exec -T db pg_isready -U $POSTGRES_USER > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Database is ready${NC}"
        break
    fi
    echo -e "${YELLOW}Waiting for database... (attempt $attempt/$max_attempts)${NC}"
    sleep 2
    ((attempt++))
done

# Check Redis
if docker-compose -f docker-compose.prod.yml exec -T redis redis-cli --no-auth-warning -a $REDIS_PASSWORD ping > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Redis is ready${NC}"
else
    echo -e "${RED}✗ Redis is not ready${NC}"
fi

# Check backend
if curl -f https://event.uog.edu.et/admin/login/ > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Backend is ready${NC}"
else
    echo -e "${RED}✗ Backend is not ready${NC}"
fi

# Check frontend
if curl -f https://event.uog.edu.et > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Frontend is ready${NC}"
else
    echo -e "${RED}✗ Frontend is not ready${NC}"
fi

# Setup log rotation
echo -e "${YELLOW}Setting up log rotation...${NC}"
cat > /etc/logrotate.d/uog-events << EOF
/var/lib/docker/volumes/uog-event_logs/_data/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF

# Setup backup cron job
echo -e "${YELLOW}Setting up backup cron job...${NC}"
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/backup-uog-events.sh") | crontab -

# Create backup script
cat > /usr/local/bin/backup-uog-events.sh << 'EOF'
#!/bin/bash
cd /path/to/uog-event
timestamp=$(date +%Y%m%d_%H%M%S)
docker-compose -f docker-compose.prod.yml exec -T db pg_dump -U $POSTGRES_USER $POSTGRES_DB > "backups/backup_${timestamp}.sql"
# Keep only last 30 days of backups
find backups/ -name "backup_*.sql" -mtime +30 -delete
EOF

chmod +x /usr/local/bin/backup-uog-events.sh

echo -e "${BLUE}============================================${NC}"
echo -e "${GREEN}Production deployment completed!${NC}"
echo -e "${BLUE}============================================${NC}"
echo -e "${YELLOW}Services:${NC}"
echo -e "Website: ${BLUE}https://event.uog.edu.et${NC}"
echo -e "Admin Panel: ${BLUE}https://event.uog.edu.et/admin${NC}"
echo -e "API: ${BLUE}https://event.uog.edu.et/api${NC}"
echo -e "${BLUE}============================================${NC}"
echo -e "${YELLOW}Important:${NC}"
echo -e "1. Change default admin password"
echo -e "2. Configure email settings"
echo -e "3. Set up SSL certificates"
echo -e "4. Configure domain DNS"
echo -e "5. Test all functionality"
echo -e "${BLUE}============================================${NC}"

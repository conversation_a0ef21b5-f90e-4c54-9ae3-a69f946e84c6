version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: event_management_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  # Redis for Celery
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

  # Django Backend (Development)
  backend:
    build: ./backend
    restart: unless-stopped
    environment:
      - SECRET_KEY=django-insecure-development-key
      - DEBUG=True
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0,backend
      - DATABASE_URL=**************************************/event_management_dev
      - REDIS_URL=redis://redis:6379/0
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
      - MEDIA_URL=/media/
      - STATIC_URL=/static/
      - MEDIA_ROOT=/app/media
      - STATIC_ROOT=/app/staticfiles
      - QR_CODE_BASE_URL=http://localhost:8000
    volumes:
      - ./backend:/app
      - media_dev_files:/app/media
      - static_dev_files:/app/staticfiles
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"

  # React Frontend (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    restart: unless-stopped
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8000/api
      - REACT_APP_BACKEND_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    stdin_open: true
    tty: true

volumes:
  postgres_dev_data:
  redis_dev_data:
  media_dev_files:
  static_dev_files:

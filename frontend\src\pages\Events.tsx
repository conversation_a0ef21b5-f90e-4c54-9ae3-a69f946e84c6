import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { eventService } from '../services/api';
import LandingNavbar from '../components/LandingNavbar';

interface Event {
  id: number;
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  location: string;
  banner_image?: string;
  is_active: boolean;
  max_participants?: number;
  registration_deadline?: string;
  event_type: string;
  created_at: string;
}

const Events: React.FC = () => {
  const [events, setEvents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'upcoming' | 'ongoing' | 'past'>('all');

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const response = await eventService.getEvents();
        // Handle paginated response
        const eventsData = response.data.results || [];
        setEvents(eventsData);
      } catch (error) {
        console.error('Error fetching events:', error);
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };
    fetchEvents();
  }, []);

  const getEventStatus = (event: any) => {
    const now = new Date();
    const startDate = new Date(event.start_date);
    const endDate = new Date(event.end_date);

    if (now < startDate) return 'upcoming';
    if (now >= startDate && now <= endDate) return 'ongoing';
    return 'past';
  };

  const filteredEvents = events.filter(event => {
    if (filter === 'all') return true;
    return getEventStatus(event) === filter;
  });

  const getStatusBadge = (status: string) => {
    const badges = {
      upcoming: 'bg-primary',
      ongoing: 'bg-success',
      past: 'bg-secondary'
    };
    return badges[status as keyof typeof badges] || 'bg-secondary';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="container-fluid py-5">
        <div className="row justify-content-center">
          <div className="col-md-6 text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3">Loading events...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="events-page">
      {/* Navigation */}
      <LandingNavbar useHomeNavigation={false} />

      {/* Header */}
      <div className="events-header py-5" style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        marginTop: '80px'
      }}>
        <div className="container">
          <div className="row">
            <div className="col-lg-8 mx-auto text-center text-white">
              <h1 className="display-4 fw-bold mb-4">University Events</h1>
              <p className="lead">
                Discover our comprehensive calendar of academic conferences, cultural celebrations, and research gatherings
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="container py-4">
        <div className="row">
          <div className="col-12">
            <ul className="nav nav-pills justify-content-center mb-4">
              <li className="nav-item">
                <button
                  className={`nav-link ${filter === 'all' ? 'active' : ''}`}
                  onClick={() => setFilter('all')}
                >
                  All Events ({events.length})
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${filter === 'upcoming' ? 'active' : ''}`}
                  onClick={() => setFilter('upcoming')}
                >
                  Upcoming ({events.filter(e => getEventStatus(e) === 'upcoming').length})
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${filter === 'ongoing' ? 'active' : ''}`}
                  onClick={() => setFilter('ongoing')}
                >
                  Ongoing ({events.filter(e => getEventStatus(e) === 'ongoing').length})
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${filter === 'past' ? 'active' : ''}`}
                  onClick={() => setFilter('past')}
                >
                  Past ({events.filter(e => getEventStatus(e) === 'past').length})
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Events Grid */}
      <div className="container pb-5">
        <div className="row g-4">
          {filteredEvents.length > 0 ? (
            filteredEvents.map((event) => {
              const status = getEventStatus(event);
              return (
                <div key={event.id} className="col-lg-4 col-md-6">
                  <div className="event-card h-100 bg-white rounded-4 shadow-sm border-0 overflow-hidden">
                    {event.banner_image && (
                      <div className="event-image">
                        <img
                          src={event.banner_image}
                          alt={event.title}
                          className="w-100"
                          style={{ height: '200px', objectFit: 'cover' }}
                        />
                      </div>
                    )}
                    <div className="card-body p-4">
                      <div className="d-flex justify-content-between align-items-start mb-3">
                        <span className={`badge ${getStatusBadge(status)} text-white px-3 py-2 rounded-pill`}>
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </span>
                        <small className="text-muted">{event.event_type}</small>
                      </div>
                      
                      <h5 className="card-title fw-bold mb-3">{event.title}</h5>
                      <p className="card-text text-muted mb-3" style={{ fontSize: '0.9rem' }}>
                        {event.description.length > 120 
                          ? event.description.substring(0, 120) + '...' 
                          : event.description
                        }
                      </p>
                      
                      <div className="event-details mb-3">
                        <div className="d-flex align-items-center mb-2">
                          <i className="fas fa-calendar-alt text-primary me-2"></i>
                          <small>{formatDate(event.start_date)} - {formatDate(event.end_date)}</small>
                        </div>
                        <div className="d-flex align-items-center mb-2">
                          <i className="fas fa-map-marker-alt text-success me-2"></i>
                          <small>{event.location}</small>
                        </div>
                        {event.max_participants && (
                          <div className="d-flex align-items-center">
                            <i className="fas fa-users text-info me-2"></i>
                            <small>Max {event.max_participants} participants</small>
                          </div>
                        )}
                      </div>
                      
                      <div className="d-flex gap-2">
                        <Link
                          to={`/events/${event.id}`}
                          className="btn btn-primary btn-sm flex-fill"
                        >
                          View Details
                        </Link>
                        {status === 'upcoming' && (
                          <Link
                            to="/register"
                            className="btn btn-outline-primary btn-sm flex-fill"
                          >
                            Register
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="col-12 text-center py-5">
              <i className="fas fa-calendar-times fa-3x text-muted mb-3"></i>
              <h4 className="text-muted">No events found</h4>
              <p className="text-muted">
                {filter === 'all' 
                  ? 'There are no events available at the moment.' 
                  : `There are no ${filter} events at the moment.`
                }
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Back to Home */}
      <div className="container pb-5">
        <div className="row">
          <div className="col-12 text-center">
            <Link to="/" className="btn btn-outline-primary btn-lg px-5">
              <i className="fas fa-arrow-left me-2"></i>
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Events;

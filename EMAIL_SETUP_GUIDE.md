# Email System Setup Guide
## University of Gondar Events Management System

This guide will help you configure and use the comprehensive email system for sending notifications to event participants.

## 📧 Features

- **Registration Confirmations**: Automatic emails when participants register
- **Event Details**: Send complete event information to participants
- **Badge Notifications**: Email badges as PDF attachments
- **Schedule Updates**: Notify participants of schedule changes
- **Daily Gallery**: Send event photos in ZIP files daily
- **Event Reminders**: Automated reminders before events
- **Email Logging**: Track all sent emails with status
- **Bulk Operations**: Send to all participants or selected groups

## 🚀 Quick Setup

### 1. Configure SMTP Settings

Update your `.env.dev` file with your email provider settings:

```env
# Email Settings
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_USE_SSL=False
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=University of Gondar Events <<EMAIL>>
```

### 2. Gmail Setup (Recommended)

1. Go to your Google Account settings
2. Enable 2-factor authentication
3. Generate an App Password:
   - Go to Security → 2-Step Verification → App passwords
   - Select "Mail" as the app
   - Copy the generated password
   - Use this password in `EMAIL_HOST_PASSWORD`

### 3. Run Migrations

```bash
cd backend
python manage.py migrate
```

### 4. Create Email Templates

```bash
python manage.py create_email_templates
```

### 5. Configure SMTP in Django Admin

1. Start your Django server
2. Go to `/admin/events/emailconfiguration/`
3. Create a new email configuration:
   - Name: "Gmail" (or your provider)
   - Fill in your SMTP settings
   - Mark as "Active"
4. Test the connection using the "Test Connection" button

## 📋 Email Templates

The system comes with pre-built templates for:

- **Registration Confirmation**: Sent automatically when users register
- **Event Details**: Complete event information
- **Badge Notification**: Badge delivery with PDF attachment
- **Schedule Update**: Schedule change notifications
- **Daily Gallery**: Daily photo packages in ZIP format
- **Event Reminder**: Automated event reminders

### Customizing Templates

1. Go to `/admin/events/emailtemplate/`
2. Edit existing templates or create new ones
3. Use template variables like `{participant_name}`, `{event_name}`, etc.
4. Preview templates before saving

## 🎛️ Email Management Interface

Access the email management interface at `/email-management` (admin only).

### Features:
- **Overview**: Email statistics and success rates
- **Send Notifications**: Bulk email sending interface
- **SMTP Config**: Manage email configurations
- **Templates**: Edit email templates
- **Email Logs**: View sent email history

## 🤖 Automated Email Sending

### Linux/Unix Systems

Run the setup script:
```bash
cd backend
chmod +x setup_email_cron.sh
./setup_email_cron.sh
```

This sets up cron jobs for:
- Daily gallery emails (8 PM daily)
- Event reminders (9 AM daily, 1 & 3 days before events)
- Email log cleanup (2 AM Sundays)

### Windows Systems

Run the setup script:
```cmd
cd backend
setup_email_tasks.bat
```

Follow the instructions to set up Windows Task Scheduler tasks.

## 📊 Manual Email Commands

### Send Daily Gallery
```bash
# Send yesterday's gallery
python manage.py send_daily_gallery

# Send specific date
python manage.py send_daily_gallery --date 2024-12-15

# Dry run (test without sending)
python manage.py send_daily_gallery --dry-run
```

### Send Event Reminders
```bash
# Send reminders for events tomorrow
python manage.py send_event_reminders --days-before 1

# Send reminders for events in 3 days
python manage.py send_event_reminders --days-before 3

# Dry run
python manage.py send_event_reminders --dry-run
```

## 🔧 API Endpoints

### Email Notifications
- `POST /events/email-notifications/send_event_details/`
- `POST /events/email-notifications/send_schedule_update/`
- `POST /events/email-notifications/send_daily_gallery/`
- `POST /events/email-notifications/send_event_reminder/`

### Email Management
- `GET /events/email-configs/` - List email configurations
- `GET /events/email-templates/` - List email templates
- `GET /events/email-logs/` - List email logs
- `GET /events/email-logs/stats/` - Email statistics

## 📈 Monitoring

### Email Logs
- View all sent emails in Django admin: `/admin/events/emaillog/`
- Check email statistics in the management interface
- Monitor failed emails and error messages

### Log Files (Linux/Unix)
- Gallery emails: `/var/log/uog_gallery_emails.log`
- Reminder emails: `/var/log/uog_reminder_emails.log`
- Email cleanup: `/var/log/uog_email_cleanup.log`

## 🛠️ Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check SMTP configuration in Django admin
   - Verify email credentials
   - Test connection in email configuration

2. **Gmail authentication errors**
   - Use App Password, not regular password
   - Enable 2-factor authentication first
   - Check "Less secure app access" if using regular password

3. **Template errors**
   - Check template variables match available context
   - Preview templates before using
   - Check for syntax errors in template content

4. **Attachment issues**
   - Ensure file paths are correct
   - Check file permissions
   - Verify file exists before sending

### Testing

```bash
# Test email configuration
python manage.py shell
>>> from events.email_service import get_email_service
>>> service = get_email_service()
>>> # Test with a participant
>>> from participants.models import Participant
>>> participant = Participant.objects.first()
>>> service.send_registration_confirmation(participant)
```

## 🔒 Security Considerations

1. **Environment Variables**: Never commit email credentials to version control
2. **App Passwords**: Use app-specific passwords for Gmail
3. **SMTP over TLS**: Always use encrypted connections
4. **Rate Limiting**: Be mindful of email provider limits
5. **Spam Prevention**: Use proper from addresses and content

## 📞 Support

For issues or questions:
1. Check the email logs in Django admin
2. Review the troubleshooting section
3. Test with dry-run commands first
4. Verify SMTP configuration

## 🎯 Best Practices

1. **Test First**: Always use `--dry-run` when testing
2. **Monitor Logs**: Regularly check email logs for failures
3. **Template Testing**: Preview templates before deployment
4. **Backup**: Keep backups of custom email templates
5. **Rate Limits**: Respect email provider sending limits
6. **Content Quality**: Use professional, clear email content

---

The email system is now fully configured and ready to handle all your event communication needs!

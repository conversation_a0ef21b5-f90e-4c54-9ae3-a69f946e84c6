from django.db import models
import qrcode
from io import BytesIO
from django.core.files import File
from PIL import Image, ImageDraw, ImageFont
from django.conf import settings
import os


class BadgeTemplate(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    width = models.IntegerField(default=400)  # pixels
    height = models.IntegerField(default=600)  # pixels
    background_color = models.CharField(max_length=7, default='#ffffff')
    template_file = models.ImageField(upload_to='badge_templates/', null=True, blank=True)
    is_default = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.is_default:
            # Ensure only one default template
            BadgeTemplate.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class Badge(models.Model):
    participant = models.OneToOneField('participants.Participant', on_delete=models.CASCADE, related_name='badge')
    template = models.ForeignKey(BadgeTemplate, on_delete=models.CASCADE, null=True, blank=True)

    # QR Code
    qr_code_data = models.TextField()  # JSON data for QR code
    qr_code_image = models.ImageField(upload_to='qr_codes/', null=True, blank=True)

    # Badge File
    badge_image = models.ImageField(upload_to='generated_badges/', null=True, blank=True)

    # Generation Status
    is_generated = models.BooleanField(default=False)
    generated_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Badge for {self.participant.full_name}"

    def generate_qr_code(self):
        """Generate QR code for the participant"""
        import json
        from django.utils import timezone

        qr_data = {
            'participant_id': str(self.participant.uuid),
            'name': self.participant.full_name,
            'event': self.participant.event.name,
            'type': self.participant.participant_type.name,
            'generated_at': timezone.now().isoformat()
        }

        self.qr_code_data = json.dumps(qr_data)

        # Generate QR code image
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(f"{settings.QR_CODE_BASE_URL}/verify/{self.participant.uuid}")
        qr.make(fit=True)

        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Save QR code image
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG')
        buffer.seek(0)

        filename = f"qr_{self.participant.uuid}.png"
        self.qr_code_image.save(filename, File(buffer), save=False)

        return qr_img

    def generate_badge(self):
        """Generate the complete badge image with improved design"""
        from django.utils import timezone

        # Get or create QR code
        if not self.qr_code_image:
            qr_img = self.generate_qr_code()
        else:
            qr_img = Image.open(self.qr_code_image.path)

        # Get template or use default dimensions
        template = self.template or BadgeTemplate.objects.filter(is_default=True).first()
        width = template.width if template else 450
        height = template.height if template else 650

        # Create badge image with gradient background
        badge_img = Image.new('RGB', (width, height), color='#ffffff')
        draw = ImageDraw.Draw(badge_img)

        # Add header banner with gradient
        banner_height = 80
        for y in range(banner_height):
            # Create gradient from blue to dark blue
            color_intensity = int(255 * (1 - y / banner_height))
            color = (30, 60 + color_intensity//4, 120 + color_intensity//2)
            draw.line([(0, y), (width, y)], fill=color)

        # Add University logo area in banner (placeholder)
        logo_text = "🇪🇹 UNIVERSITY OF GONDAR"
        try:
            banner_font = ImageFont.truetype("arial.ttf", 16)
        except:
            banner_font = ImageFont.load_default()

        logo_bbox = draw.textbbox((0, 0), logo_text, font=banner_font)
        logo_width = logo_bbox[2] - logo_bbox[0]
        draw.text((width//2 - logo_width//2, 25), logo_text, fill='white', font=banner_font)

        # Load improved fonts with better styling
        try:
            title_font = ImageFont.truetype("arial.ttf", 28)  # Larger title
            name_font = ImageFont.truetype("arialbd.ttf", 24)  # Bold name font
            info_font = ImageFont.truetype("arial.ttf", 16)   # Larger info font
            small_font = ImageFont.truetype("arial.ttf", 12)  # Small text
        except:
            title_font = ImageFont.load_default()
            name_font = ImageFont.load_default()
            info_font = ImageFont.load_default()
            small_font = ImageFont.load_default()

        # Add participant photo if available (moved down)
        photo_y = 120  # Moved down from banner
        if self.participant.profile_photo:
            try:
                photo = Image.open(self.participant.profile_photo.path)
                # Make photo circular
                photo = photo.resize((140, 140))
                # Create circular mask
                mask = Image.new('L', (140, 140), 0)
                mask_draw = ImageDraw.Draw(mask)
                mask_draw.ellipse((0, 0, 140, 140), fill=255)

                # Apply circular mask
                photo.putalpha(mask)
                badge_img.paste(photo, (width//2 - 70, photo_y), photo)

                # Add photo border
                draw.ellipse((width//2 - 72, photo_y - 2, width//2 + 72, photo_y + 142),
                           outline='#1e3a8a', width=3)
            except Exception as e:
                # Fallback: draw placeholder circle
                draw.ellipse((width//2 - 70, photo_y, width//2 + 70, photo_y + 140),
                           fill='#f0f0f0', outline='#cccccc', width=2)
                draw.text((width//2 - 25, photo_y + 60), "PHOTO", fill='#666666', font=info_font)

        # Add participant name (bold and prominent)
        name = self.participant.full_name.upper()
        name_y = photo_y + 160
        name_bbox = draw.textbbox((0, 0), name, font=name_font)
        name_width = name_bbox[2] - name_bbox[0]
        # Add text shadow for better visibility
        draw.text((width//2 - name_width//2 + 1, name_y + 1), name, fill='#cccccc', font=name_font)
        draw.text((width//2 - name_width//2, name_y), name, fill='#1e3a8a', font=name_font)

        # Add participant type with colored background
        ptype = self.participant.participant_type.name.upper()
        ptype_y = name_y + 40
        ptype_bbox = draw.textbbox((0, 0), ptype, font=info_font)
        ptype_width = ptype_bbox[2] - ptype_bbox[0]
        ptype_height = ptype_bbox[3] - ptype_bbox[1]

        # Draw colored background for participant type
        bg_padding = 8
        draw.rectangle([
            width//2 - ptype_width//2 - bg_padding,
            ptype_y - bg_padding//2,
            width//2 + ptype_width//2 + bg_padding,
            ptype_y + ptype_height + bg_padding//2
        ], fill=self.participant.participant_type.color)

        draw.text((width//2 - ptype_width//2, ptype_y), ptype, fill='white', font=info_font)

        # Add institution
        institution = self.participant.institution_name
        if len(institution) > 35:
            institution = institution[:32] + "..."
        inst_y = ptype_y + 50
        inst_bbox = draw.textbbox((0, 0), institution, font=info_font)
        inst_width = inst_bbox[2] - inst_bbox[0]
        draw.text((width//2 - inst_width//2, inst_y), institution, fill='#374151', font=info_font)

        # Add larger QR code with border
        qr_size = 140  # Increased from 100
        qr_y = inst_y + 50
        qr_img_resized = qr_img.resize((qr_size, qr_size))

        # Add QR code background and border
        qr_bg_padding = 10
        draw.rectangle([
            width//2 - qr_size//2 - qr_bg_padding,
            qr_y - qr_bg_padding,
            width//2 + qr_size//2 + qr_bg_padding,
            qr_y + qr_size + qr_bg_padding
        ], fill='white', outline='#1e3a8a', width=2)

        badge_img.paste(qr_img_resized, (width//2 - qr_size//2, qr_y))

        # Add QR code label
        qr_label = "SCAN FOR VERIFICATION"
        qr_label_y = qr_y + qr_size + 20
        qr_label_bbox = draw.textbbox((0, 0), qr_label, font=small_font)
        qr_label_width = qr_label_bbox[2] - qr_label_bbox[0]
        draw.text((width//2 - qr_label_width//2, qr_label_y), qr_label, fill='#6b7280', font=small_font)

        # Add footer banner with event name
        footer_y = height - 60
        footer_height = 60

        # Draw footer banner
        for y in range(footer_height):
            color_intensity = int(255 * (y / footer_height))
            color = (30, 60 + color_intensity//4, 120 + color_intensity//2)
            draw.line([(0, footer_y + y), (width, footer_y + y)], fill=color)

        # Add event name in footer
        event_name = self.participant.event.name
        if len(event_name) > 30:
            event_name = event_name[:27] + "..."
        event_bbox = draw.textbbox((0, 0), event_name, font=info_font)
        event_width = event_bbox[2] - event_bbox[0]
        draw.text((width//2 - event_width//2, footer_y + 20), event_name, fill='white', font=info_font)

        # Save badge image
        buffer = BytesIO()
        badge_img.save(buffer, format='PNG')
        buffer.seek(0)

        filename = f"badge_{self.participant.uuid}.png"
        self.badge_image.save(filename, File(buffer), save=False)

        self.is_generated = True
        self.generated_at = timezone.now()
        self.save()

        return badge_img

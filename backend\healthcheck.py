#!/usr/bin/env python
"""
Health check script for Django backend
"""
import os
import sys
import django
from django.conf import settings

# Add the project directory to the Python path
sys.path.append('/app')

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

try:
    from django.db import connections
    from django.core.cache import cache
    import requests
    
    # Check database connection
    db_conn = connections['default']
    db_conn.cursor()
    
    # Check cache (Redis) connection
    cache.set('health_check', 'ok', 30)
    cache_result = cache.get('health_check')
    
    if cache_result != 'ok':
        raise Exception("Cache check failed")
    
    # Check if the web server is responding
    import os
    health_check_url = os.environ.get('HEALTH_CHECK_URL', 'http://localhost:8000/admin/login/')
    response = requests.get(health_check_url, timeout=5)
    if response.status_code not in [200, 302]:
        raise Exception(f"Web server check failed with status {response.status_code}")
    
    print("Health check passed")
    sys.exit(0)
    
except Exception as e:
    print(f"Health check failed: {e}")
    sys.exit(1)

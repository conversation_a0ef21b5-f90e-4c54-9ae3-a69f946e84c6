#!/usr/bin/env python
import os
import sys
import django
from datetime import timedelta
from django.utils import timezone

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event, EmailLog
from events.email_service import get_email_service

def test_registration_flow():
    print("=== Testing Registration Flow ===")
    
    # Get required objects
    event = Event.objects.first()
    participant_type = ParticipantType.objects.first()
    
    if not event or not participant_type:
        print("❌ Missing event or participant type")
        return
    
    print(f"Using event: {event.name}")
    print(f"Using participant type: {participant_type.name}")
    
    # Create test participant
    test_email = f"test.registration.{timezone.now().strftime('%Y%m%d%H%M%S')}@example.com"
    
    try:
        participant = Participant.objects.create(
            event=event,
            first_name='Test',
            last_name='Registration Flow',
            email=test_email,
            phone='+251911123456',
            institution_name='Test Institution',
            position='Test Position',
            participant_type=participant_type,
            arrival_date=timezone.now() + timedelta(days=1),
            departure_date=timezone.now() + timedelta(days=3),
            status='pending'
        )
        
        print(f"✅ Created participant: {participant.full_name}")
        print(f"   Email: {participant.email}")
        
        # Test manual email sending
        email_service = get_email_service()
        result = email_service.send_registration_confirmation(participant)
        
        if result:
            print("✅ Registration confirmation email sent successfully!")
            
            # Check email log
            email_log = EmailLog.objects.filter(
                recipient_email=participant.email,
                template_type='registration_confirmation'
            ).order_by('-created_at').first()
            
            if email_log:
                print(f"✅ Email logged with status: {email_log.status}")
                if email_log.error_message:
                    print(f"   Error: {email_log.error_message}")
            else:
                print("❌ Email not found in logs")
        else:
            print("❌ Failed to send registration confirmation email")
        
        # Clean up
        participant.delete()
        print("✅ Test participant cleaned up")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")

def test_serializer_flow():
    print("\n=== Testing Serializer Registration Flow ===")
    
    from participants.serializers import ParticipantRegistrationSerializer
    from django.core.files.uploadedfile import SimpleUploadedFile
    
    # Get required objects
    event = Event.objects.first()
    participant_type = ParticipantType.objects.first()
    
    if not event or not participant_type:
        print("❌ Missing event or participant type")
        return
    
    # Create fake image file
    image_content = b'fake image content for testing'
    image_file = SimpleUploadedFile('test.jpg', image_content, content_type='image/jpeg')
    
    test_email = f"test.serializer.{timezone.now().strftime('%Y%m%d%H%M%S')}@example.com"
    
    registration_data = {
        'first_name': 'Test',
        'last_name': 'Serializer Flow',
        'email': test_email,
        'phone': '+251911123456',
        'institution_name': 'Test Institution',
        'position': 'Test Position',
        'event': event.id,
        'participant_type': participant_type.id,
        'arrival_date': timezone.now() + timedelta(days=1),
        'departure_date': timezone.now() + timedelta(days=3),
        # 'profile_photo': image_file,  # Skip image for testing
        'remarks': 'Test registration via serializer'
    }
    
    try:
        serializer = ParticipantRegistrationSerializer(data=registration_data)
        if serializer.is_valid():
            participant = serializer.save()
            print(f"✅ Participant created via serializer: {participant.full_name}")
            
            # Check if email was automatically sent
            email_log = EmailLog.objects.filter(
                recipient_email=participant.email,
                template_type='registration_confirmation'
            ).order_by('-created_at').first()
            
            if email_log:
                print(f"✅ Automatic email logged with status: {email_log.status}")
                if email_log.error_message:
                    print(f"   Error: {email_log.error_message}")
            else:
                print("❌ No automatic email found in logs")
            
            # Clean up
            participant.delete()
            print("✅ Test participant cleaned up")
        else:
            print(f"❌ Serializer validation failed: {serializer.errors}")
            
    except Exception as e:
        print(f"❌ Error during serializer test: {e}")

if __name__ == "__main__":
    test_registration_flow()
    test_serializer_flow()

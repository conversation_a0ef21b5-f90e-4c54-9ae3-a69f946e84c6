# UoG Event Management System Startup Script
# PowerShell version for better error handling and cross-platform support

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "UoG Event Management System Startup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Change to the project directory
$projectPath = "C:\Users\<USER>\Desktop\uog event"
Set-Location $projectPath

try {
    Write-Host "[1/7] Stopping any existing containers..." -ForegroundColor Yellow
    docker-compose down
    Write-Host ""

    Write-Host "[2/7] Building all services (this may take a few minutes)..." -ForegroundColor Yellow
    $buildResult = docker-compose build --no-cache
    if ($LASTEXITCODE -ne 0) {
        throw "Docker build failed with exit code $LASTEXITCODE"
    }
    Write-Host "✓ Build completed successfully" -ForegroundColor Green
    Write-Host ""

    Write-Host "[3/7] Starting database and Redis..." -ForegroundColor Yellow
    docker-compose up -d db redis
    Write-Host "Waiting for database to initialize..." -ForegroundColor Gray
    Start-Sleep -Seconds 15
    Write-Host "✓ Database and Redis started" -ForegroundColor Green
    Write-Host ""

    Write-Host "[4/7] Starting backend service..." -ForegroundColor Yellow
    docker-compose up -d backend
    Write-Host "Waiting for backend to initialize..." -ForegroundColor Gray
    Start-Sleep -Seconds 20
    Write-Host "✓ Backend service started" -ForegroundColor Green
    Write-Host ""

    Write-Host "[5/7] Testing backend API connectivity..." -ForegroundColor Yellow
    $maxRetries = 5
    $retryCount = 0
    $apiReady = $false
    
    while ($retryCount -lt $maxRetries -and -not $apiReady) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8000/api/events/" -UseBasicParsing -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Host "✓ Backend API is responding (HTTP $($response.StatusCode))" -ForegroundColor Green
                $apiReady = $true
            }
        }
        catch {
            $retryCount++
            Write-Host "Attempt $retryCount/$maxRetries - API not ready yet, retrying..." -ForegroundColor Gray
            Start-Sleep -Seconds 5
        }
    }
    
    if (-not $apiReady) {
        Write-Host "⚠ Backend API may not be fully ready, but continuing..." -ForegroundColor Yellow
    }
    Write-Host ""

    Write-Host "[6/7] Installing frontend dependencies..." -ForegroundColor Yellow
    Set-Location "$projectPath\frontend"
    npm install --silent
    Write-Host "✓ Frontend dependencies installed" -ForegroundColor Green
    Write-Host ""

    Write-Host "[7/7] Starting frontend development server..." -ForegroundColor Yellow
    Write-Host "Opening new terminal for frontend..." -ForegroundColor Gray
    
    # Start frontend in a new PowerShell window
    $frontendScript = @"
Set-Location '$projectPath\frontend'
Write-Host 'Starting React development server...' -ForegroundColor Cyan
npm start
"@

    Start-Process powershell -ArgumentList @("-NoExit", "-Command", $frontendScript)
    Write-Host "✓ Frontend server starting in new window" -ForegroundColor Green
    Write-Host ""

    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "🎉 Application Startup Complete!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "📊 Services Status:" -ForegroundColor White
    Write-Host "- Backend API: " -NoNewline; Write-Host "http://localhost:8000" -ForegroundColor Blue
    Write-Host "- Frontend: " -NoNewline; Write-Host "http://localhost:3001" -ForegroundColor Blue
    Write-Host "- Database: " -NoNewline; Write-Host "PostgreSQL on port 5432" -ForegroundColor Blue
    Write-Host "- Redis: " -NoNewline; Write-Host "Redis on port 6379" -ForegroundColor Blue
    Write-Host ""
    
    Write-Host "🔧 Management URLs:" -ForegroundColor White
    Write-Host "- Admin Panel: " -NoNewline; Write-Host "http://localhost:8000/admin/" -ForegroundColor Blue
    Write-Host "- API Docs: " -NoNewline; Write-Host "http://localhost:8000/api/" -ForegroundColor Blue
    Write-Host ""
    
    Write-Host "🆕 New Features Available:" -ForegroundColor White
    Write-Host "- Hotel Management: " -NoNewline; Write-Host "http://localhost:3001/hotels" -ForegroundColor Blue
    Write-Host "- Driver Management: " -NoNewline; Write-Host "http://localhost:3001/drivers" -ForegroundColor Blue
    Write-Host "- Participant Types: " -NoNewline; Write-Host "http://localhost:3001/participant-types" -ForegroundColor Blue
    Write-Host ""

    Write-Host "Checking Docker container status..." -ForegroundColor Gray
    docker-compose ps
    Write-Host ""

    Write-Host "Opening application in browser..." -ForegroundColor Gray
    Start-Sleep -Seconds 3
    Start-Process "http://localhost:3001"

    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "✅ Startup completed successfully!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "💡 Useful Commands:" -ForegroundColor White
    Write-Host "- Stop all services: " -NoNewline; Write-Host "docker-compose down" -ForegroundColor Yellow
    Write-Host "- View logs: " -NoNewline; Write-Host "docker-compose logs -f" -ForegroundColor Yellow
    Write-Host "- Restart backend: " -NoNewline; Write-Host "docker-compose restart backend" -ForegroundColor Yellow
    Write-Host ""

}
catch {
    Write-Host ""
    Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Make sure Docker Desktop is running"
    Write-Host "2. Check if ports 3001, 8000, 5432, 6379 are available"
    Write-Host "3. Try running: docker-compose down && docker system prune -f"
    Write-Host "4. Restart Docker Desktop and try again"
    Write-Host ""
}

Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api';

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone: string;
  position: string;
  institution: string;
  profile_picture?: string;
  role: number;
  role_name: string;
  role_display_name: string;
  role_color: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  date_joined: string;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface UserRole {
  id: number;
  name: string;
  display_name: string;
  description: string;
  color: string;
  can_manage_users: boolean;
  can_manage_events: boolean;
  can_manage_participants: boolean;
  can_take_attendance: boolean;
  can_upload_gallery: boolean;
  can_manage_schedule: boolean;
  can_generate_badges: boolean;
  can_manage_hotels: boolean;
  can_manage_drivers: boolean;
  can_view_reports: boolean;
  can_export_data: boolean;
}

export interface UserPermissions {
  user_id: number;
  username: string;
  role: string;
  role_display: string;
  permissions: {
    can_manage_users: boolean;
    can_manage_events: boolean;
    can_manage_participants: boolean;
    can_take_attendance: boolean;
    can_upload_gallery: boolean;
    can_manage_schedule: boolean;
    can_generate_badges: boolean;
    can_manage_hotels: boolean;
    can_manage_drivers: boolean;
    can_view_reports: boolean;
    can_export_data: boolean;
  };
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  access: string;
  refresh: string;
  user: User;
}

export interface DashboardStats {
  total_users?: number;
  active_users?: number;
  total_sessions?: number;
  recent_logins?: number;
  total_events?: number;
  active_events?: number;
  total_participants?: number;
  confirmed_participants?: number;
  total_attendance?: number;
  today_attendance?: number;
  total_photos?: number;
  featured_photos?: number;
}

class AuthService {
  private baseURL = `${API_BASE_URL}/auth`;

  // Set up axios interceptor for token
  constructor() {
    axios.interceptors.request.use(
      (config) => {
        const token = this.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            await this.refreshToken();
            const token = this.getAccessToken();
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return axios(originalRequest);
          } catch (refreshError) {
            this.logout();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }
        
        return Promise.reject(error);
      }
    );
  }

  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await axios.post(`${this.baseURL}/login/`, credentials);
    const data = response.data;
    
    // Store tokens
    localStorage.setItem('access_token', data.access);
    localStorage.setItem('refresh_token', data.refresh);
    localStorage.setItem('user', JSON.stringify(data.user));
    
    return data;
  }

  async logout(): Promise<void> {
    try {
      const refreshToken = this.getRefreshToken();
      if (refreshToken) {
        await axios.post(`${this.baseURL}/logout/`, {
          refresh_token: refreshToken
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
    }
  }

  async refreshToken(): Promise<string> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await axios.post(`${this.baseURL}/token/refresh/`, {
      refresh: refreshToken
    });

    const newAccessToken = response.data.access;
    localStorage.setItem('access_token', newAccessToken);
    
    return newAccessToken;
  }

  async getUserProfile(): Promise<User> {
    const response = await axios.get(`${this.baseURL}/profile/`);
    const user = response.data;
    localStorage.setItem('user', JSON.stringify(user));
    return user;
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await axios.patch(`${this.baseURL}/profile/`, userData);
    const user = response.data;
    localStorage.setItem('user', JSON.stringify(user));
    return user;
  }

  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    await axios.post(`${this.baseURL}/change-password/`, {
      old_password: oldPassword,
      new_password: newPassword,
      new_password_confirm: newPassword
    });
  }

  async getUserPermissions(): Promise<UserPermissions> {
    const response = await axios.get(`${this.baseURL}/permissions/`);
    return response.data;
  }

  async getDashboardStats(): Promise<DashboardStats> {
    const response = await axios.get(`${this.baseURL}/dashboard/stats/`);
    return response.data;
  }

  async getUsers(): Promise<User[]> {
    const response = await axios.get(`${this.baseURL}/users/`);
    return response.data;
  }

  async createUser(userData: any): Promise<User> {
    const response = await axios.post(`${this.baseURL}/users/`, userData);
    return response.data;
  }

  async updateUser(userId: number, userData: Partial<User>): Promise<User> {
    const response = await axios.patch(`${this.baseURL}/users/${userId}/`, userData);
    return response.data;
  }

  async deleteUser(userId: number): Promise<void> {
    await axios.delete(`${this.baseURL}/users/${userId}/`);
  }

  async getUserRoles(): Promise<UserRole[]> {
    const response = await axios.get(`${this.baseURL}/roles/`);
    return response.data;
  }

  // Token management
  getAccessToken(): string | null {
    return localStorage.getItem('access_token');
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token');
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }



  getUserRoleColor(): string {
    const user = this.getCurrentUser();
    return user?.role_color || '#6c757d';
  }

  getUserRoleDisplayName(): string {
    const user = this.getCurrentUser();
    return user?.role_display_name || 'Participant';
  }
}

export const authService = new AuthService();
export default authService;

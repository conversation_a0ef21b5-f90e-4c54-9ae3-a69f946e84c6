#!/bin/bash

# Setup script for automated email cron jobs
# This script sets up cron jobs for automated email sending

# Get the current directory (should be the backend directory)
BACKEND_DIR=$(pwd)
PYTHON_PATH=$(which python)
MANAGE_PY="$BACKEND_DIR/manage.py"

echo "Setting up automated email cron jobs..."
echo "Backend directory: $BACKEND_DIR"
echo "Python path: $PYTHON_PATH"
echo "Manage.py path: $MANAGE_PY"

# Check if manage.py exists
if [ ! -f "$MANAGE_PY" ]; then
    echo "Error: manage.py not found in current directory"
    echo "Please run this script from the Django backend directory"
    exit 1
fi

# Create a temporary cron file
TEMP_CRON=$(mktemp)

# Get existing cron jobs (excluding our email jobs)
crontab -l 2>/dev/null | grep -v "# UoG Events Email" > "$TEMP_CRON"

echo "" >> "$TEMP_CRON"
echo "# UoG Events Email Automation Jobs" >> "$TEMP_CRON"
echo "# Generated on $(date)" >> "$TEMP_CRON"
echo "" >> "$TEMP_CRON"

# Daily gallery emails - Send at 8 PM every day
echo "# Send daily gallery emails at 8 PM" >> "$TEMP_CRON"
echo "0 20 * * * cd $BACKEND_DIR && $PYTHON_PATH $MANAGE_PY send_daily_gallery >> /var/log/uog_gallery_emails.log 2>&1 # UoG Events Email" >> "$TEMP_CRON"
echo "" >> "$TEMP_CRON"

# Event reminders - Send at 9 AM every day (1 day before events)
echo "# Send event reminders at 9 AM (1 day before events)" >> "$TEMP_CRON"
echo "0 9 * * * cd $BACKEND_DIR && $PYTHON_PATH $MANAGE_PY send_event_reminders --days-before 1 >> /var/log/uog_reminder_emails.log 2>&1 # UoG Events Email" >> "$TEMP_CRON"
echo "" >> "$TEMP_CRON"

# Event reminders - Send at 9 AM every day (3 days before events)
echo "# Send event reminders at 9 AM (3 days before events)" >> "$TEMP_CRON"
echo "0 9 * * * cd $BACKEND_DIR && $PYTHON_PATH $MANAGE_PY send_event_reminders --days-before 3 >> /var/log/uog_reminder_emails.log 2>&1 # UoG Events Email" >> "$TEMP_CRON"
echo "" >> "$TEMP_CRON"

# Weekly email statistics cleanup - Run at 2 AM every Sunday
echo "# Clean up old email logs at 2 AM every Sunday (keep last 30 days)" >> "$TEMP_CRON"
echo "0 2 * * 0 cd $BACKEND_DIR && $PYTHON_PATH $MANAGE_PY shell -c \"from events.models import EmailLog; from django.utils import timezone; from datetime import timedelta; EmailLog.objects.filter(created_at__lt=timezone.now() - timedelta(days=30)).delete()\" >> /var/log/uog_email_cleanup.log 2>&1 # UoG Events Email" >> "$TEMP_CRON"

# Install the new cron jobs
crontab "$TEMP_CRON"

# Clean up temporary file
rm "$TEMP_CRON"

echo ""
echo "Cron jobs installed successfully!"
echo ""
echo "The following automated email jobs have been set up:"
echo "1. Daily gallery emails - 8:00 PM every day"
echo "2. Event reminders (1 day before) - 9:00 AM every day"
echo "3. Event reminders (3 days before) - 9:00 AM every day"
echo "4. Email log cleanup - 2:00 AM every Sunday"
echo ""
echo "Log files will be created in:"
echo "- /var/log/uog_gallery_emails.log"
echo "- /var/log/uog_reminder_emails.log"
echo "- /var/log/uog_email_cleanup.log"
echo ""
echo "To view current cron jobs: crontab -l"
echo "To remove these jobs, run: crontab -l | grep -v '# UoG Events Email' | crontab -"
echo ""
echo "Note: Make sure your Django application is properly configured with:"
echo "1. SMTP settings in your environment variables"
echo "2. Active email configuration in Django admin"
echo "3. Email templates created and activated"
echo ""
echo "You can test the commands manually:"
echo "  python manage.py send_daily_gallery --dry-run"
echo "  python manage.py send_event_reminders --dry-run"

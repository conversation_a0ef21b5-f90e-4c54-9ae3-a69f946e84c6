# Simple Startup Script for UoG Event Management System

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "UoG Event Management System - Simple Start" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$projectPath = "C:\Users\<USER>\Desktop\uog event"
Set-Location $projectPath

Write-Host "Step 1: Stopping existing containers..." -ForegroundColor Yellow
docker-compose down

Write-Host "Step 2: Building services..." -ForegroundColor Yellow
docker-compose build --no-cache

Write-Host "Step 3: Starting all services..." -ForegroundColor Yellow
docker-compose up -d

Write-Host "Step 4: Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 20

Write-Host "Step 5: Testing backend API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/events/" -UseBasicParsing -TimeoutSec 10
    Write-Host "Backend API is ready! (HTTP $($response.StatusCode))" -ForegroundColor Green
}
catch {
    Write-Host "Backend API not ready yet, but continuing..." -ForegroundColor Yellow
}

Write-Host "Step 6: Starting frontend..." -ForegroundColor Yellow
Set-Location "$projectPath\frontend"
Start-Process cmd -ArgumentList "/k", "npm install && npm start"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Startup Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Services:" -ForegroundColor White
Write-Host "- Backend: http://localhost:8000" -ForegroundColor Blue
Write-Host "- Frontend: http://localhost:3001" -ForegroundColor Blue
Write-Host ""
Write-Host "New Features:" -ForegroundColor White
Write-Host "- Hotels: http://localhost:3001/hotels" -ForegroundColor Blue
Write-Host "- Drivers: http://localhost:3001/drivers" -ForegroundColor Blue
Write-Host "- Participant Types: http://localhost:3001/participant-types" -ForegroundColor Blue
Write-Host ""

# Wait a bit then open browser
Start-Sleep -Seconds 5
Start-Process "http://localhost:3001"

Write-Host "Browser opened. Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

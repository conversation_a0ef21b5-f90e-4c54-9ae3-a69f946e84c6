# University of Gondar Event Management System - Production Deployment Script (PowerShell)

param(
    [switch]$Force,
    [switch]$NoBackup
)

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Blue = "Cyan"

Write-Host "============================================" -ForegroundColor $Blue
Write-Host "UoG Event Management - Production Deployment" -ForegroundColor $Blue
Write-Host "============================================" -ForegroundColor $Blue

# Check if running as administrator
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
if (-not $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Host "Please run this script as Administrator" -ForegroundColor $Red
    exit 1
}

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor $Green
} catch {
    Write-Host "✗ Error: Docker is not running. Please start Docker and try again." -ForegroundColor $Red
    exit 1
}

# Check if Docker Compose is available
try {
    docker-compose --version | Out-Null
    Write-Host "✓ Docker Compose is available" -ForegroundColor $Green
} catch {
    Write-Host "✗ Error: Docker Compose is not installed." -ForegroundColor $Red
    exit 1
}

# Load environment variables
if (Test-Path ".env.prod") {
    Write-Host "Loading production environment variables..." -ForegroundColor $Yellow
    Get-Content ".env.prod" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
} else {
    Write-Host "Error: .env.prod file not found. Please create it from .env.prod template" -ForegroundColor $Red
    exit 1
}

# Validate required environment variables
$requiredVars = @("SECRET_KEY", "POSTGRES_PASSWORD", "REDIS_PASSWORD", "EMAIL_HOST_PASSWORD")
foreach ($var in $requiredVars) {
    if (-not [Environment]::GetEnvironmentVariable($var)) {
        Write-Host "Error: Required environment variable $var is not set" -ForegroundColor $Red
        exit 1
    }
}

# Create necessary directories
Write-Host "Creating necessary directories..." -ForegroundColor $Yellow
New-Item -ItemType Directory -Force -Path "logs\nginx" | Out-Null
New-Item -ItemType Directory -Force -Path "backups" | Out-Null
New-Item -ItemType Directory -Force -Path "nginx\ssl" | Out-Null

# Check SSL certificates
if (-not (Test-Path "nginx\ssl\cert.pem") -or -not (Test-Path "nginx\ssl\private.key")) {
    Write-Host "SSL certificates not found. Please place your SSL certificates in nginx\ssl\" -ForegroundColor $Yellow
    Write-Host "Required files: cert.pem and private.key" -ForegroundColor $Yellow
    if (-not $Force) {
        $continue = Read-Host "Continue without SSL? (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            exit 1
        }
    }
}

# Backup existing data (if any)
if (-not $NoBackup) {
    $runningContainers = docker-compose -f docker-compose.prod.yml ps --services --filter "status=running"
    if ($runningContainers) {
        Write-Host "Creating backup of existing data..." -ForegroundColor $Yellow
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupFile = "backups\backup_$timestamp.sql"
        
        try {
            docker-compose -f docker-compose.prod.yml exec -T db pg_dump -U $env:POSTGRES_USER $env:POSTGRES_DB > $backupFile
            Write-Host "Backup created: $backupFile" -ForegroundColor $Green
        } catch {
            Write-Host "Warning: Could not create backup" -ForegroundColor $Yellow
        }
    }
}

# Stop existing containers
Write-Host "Stopping existing containers..." -ForegroundColor $Yellow
docker-compose -f docker-compose.prod.yml down

# Build production images
Write-Host "Building production images..." -ForegroundColor $Yellow
docker-compose -f docker-compose.prod.yml build --no-cache

# Start services
Write-Host "Starting production services..." -ForegroundColor $Yellow
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be ready
Write-Host "Waiting for services to be ready..." -ForegroundColor $Yellow
Start-Sleep -Seconds 30

# Check service health
Write-Host "Checking service health..." -ForegroundColor $Yellow

# Check database
$maxAttempts = 30
$attempt = 1
while ($attempt -le $maxAttempts) {
    try {
        docker-compose -f docker-compose.prod.yml exec -T db pg_isready -U $env:POSTGRES_USER | Out-Null
        Write-Host "✓ Database is ready" -ForegroundColor $Green
        break
    } catch {
        Write-Host "Waiting for database... (attempt $attempt/$maxAttempts)" -ForegroundColor $Yellow
        Start-Sleep -Seconds 2
        $attempt++
    }
}

# Check Redis
try {
    docker-compose -f docker-compose.prod.yml exec -T redis redis-cli --no-auth-warning -a $env:REDIS_PASSWORD ping | Out-Null
    Write-Host "✓ Redis is ready" -ForegroundColor $Green
} catch {
    Write-Host "✗ Redis is not ready" -ForegroundColor $Red
}

# Check backend
try {
    $response = Invoke-WebRequest -Uri "https://event.uog.edu.et/admin/login/" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Backend is ready" -ForegroundColor $Green
    }
} catch {
    Write-Host "✗ Backend is not ready" -ForegroundColor $Red
}

# Check frontend
try {
    $response = Invoke-WebRequest -Uri "https://event.uog.edu.et" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Frontend is ready" -ForegroundColor $Green
    }
} catch {
    Write-Host "✗ Frontend is not ready" -ForegroundColor $Red
}

Write-Host "============================================" -ForegroundColor $Blue
Write-Host "Production deployment completed!" -ForegroundColor $Green
Write-Host "============================================" -ForegroundColor $Blue
Write-Host "Services:" -ForegroundColor $Yellow
Write-Host "Website: https://event.uog.edu.et" -ForegroundColor $Blue
Write-Host "Admin Panel: https://event.uog.edu.et/admin" -ForegroundColor $Blue
Write-Host "API: https://event.uog.edu.et/api" -ForegroundColor $Blue
Write-Host "============================================" -ForegroundColor $Blue
Write-Host "Important:" -ForegroundColor $Yellow
Write-Host "1. Change default admin password" -ForegroundColor $Yellow
Write-Host "2. Configure email settings" -ForegroundColor $Yellow
Write-Host "3. Set up SSL certificates" -ForegroundColor $Yellow
Write-Host "4. Configure domain DNS" -ForegroundColor $Yellow
Write-Host "5. Test all functionality" -ForegroundColor $Yellow
Write-Host "============================================" -ForegroundColor $Blue

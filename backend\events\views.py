from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from django.utils import timezone
from django.contrib.auth import get_user_model
import csv
import io
from .models import Event, EventSchedule, EventGallery

User = get_user_model()
from .serializers import (
    EventSerializer, EventListSerializer,
    EventScheduleSerializer, EventGallerySerializer
)


class EventViewSet(viewsets.ModelViewSet):
    queryset = Event.objects.all()

    def get_permissions(self):
        """
        Allow public access for list, retrieve, schedule, and gallery actions,
        require authentication for create, update, delete, and participants
        """
        if self.action in ['list', 'retrieve', 'schedule', 'gallery']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        if self.action == 'list':
            return EventListSerializer
        return EventSerializer

    @action(detail=True, methods=['get'])
    def schedule(self, request, pk=None):
        """Get event schedule"""
        event = self.get_object()
        schedules = event.schedules.all()
        serializer = EventScheduleSerializer(schedules, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def gallery(self, request, pk=None):
        """Get event gallery"""
        event = self.get_object()
        gallery = event.gallery.all()
        serializer = EventGallerySerializer(gallery, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def participants(self, request, pk=None):
        """Get event participants"""
        event = self.get_object()
        from participants.serializers import ParticipantSerializer
        participants = event.participants.all()
        serializer = ParticipantSerializer(participants, many=True)
        return Response(serializer.data)


class EventScheduleViewSet(viewsets.ModelViewSet):
    queryset = EventSchedule.objects.all()
    serializer_class = EventScheduleSerializer

    def get_permissions(self):
        """Allow public access for list and retrieve actions"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        event_id = self.request.query_params.get('event', None)
        if event_id is not None:
            queryset = queryset.filter(event=event_id)
        return queryset


class EventGalleryViewSet(viewsets.ModelViewSet):
    queryset = EventGallery.objects.all()
    serializer_class = EventGallerySerializer

    def get_permissions(self):
        """Allow public access for list and retrieve actions"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        event_id = self.request.query_params.get('event', None)
        if event_id is not None:
            queryset = queryset.filter(event=event_id)
        return queryset

    def perform_create(self, serializer):
        """Set the uploaded_by field to current user"""
        serializer.save(uploaded_by=self.request.user if self.request.user.is_authenticated else None)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured gallery images"""
        featured = self.get_queryset().filter(is_featured=True)
        serializer = self.get_serializer(featured, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_date(self, request):
        """Get gallery images by date"""
        date = request.query_params.get('date')
        if not date:
            return Response({'error': 'Date parameter is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            from datetime import datetime
            date_obj = datetime.strptime(date, '%Y-%m-%d').date()
            images = self.get_queryset().filter(uploaded_at__date=date_obj)
            serializer = self.get_serializer(images, many=True)
            return Response(serializer.data)
        except ValueError:
            return Response({'error': 'Invalid date format. Use YYYY-MM-DD'}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def toggle_featured(self, request, pk=None):
        """Toggle featured status of gallery image"""
        image = self.get_object()
        image.is_featured = not image.is_featured
        image.save()
        serializer = self.get_serializer(image)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def bulk_upload(self, request):
        """Bulk upload multiple images"""
        event_id = request.data.get('event')
        if not event_id:
            return Response({'error': 'Event ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            event = Event.objects.get(id=event_id)
        except Event.DoesNotExist:
            return Response({'error': 'Event not found'}, status=status.HTTP_404_NOT_FOUND)

        uploaded_images = []
        errors = []

        # Process multiple files
        for key, file in request.FILES.items():
            if key.startswith('image_'):
                try:
                    title = request.data.get(f'title_{key.split("_")[1]}', f'Gallery Image {len(uploaded_images) + 1}')
                    description = request.data.get(f'description_{key.split("_")[1]}', '')

                    gallery_image = EventGallery.objects.create(
                        event=event,
                        title=title,
                        description=description,
                        image=file,
                        uploaded_by=request.user if request.user.is_authenticated else None
                    )
                    uploaded_images.append(gallery_image)
                except Exception as e:
                    errors.append(f'Error uploading {file.name}: {str(e)}')

        if uploaded_images:
            serializer = self.get_serializer(uploaded_images, many=True)
            response_data = {
                'uploaded': len(uploaded_images),
                'errors': len(errors),
                'images': serializer.data
            }
            if errors:
                response_data['error_details'] = errors
            return Response(response_data, status=status.HTTP_201_CREATED)
        else:
            return Response({'error': 'No images were uploaded', 'details': errors},
                          status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['delete'])
    def bulk_delete(self, request):
        """Bulk delete gallery images"""
        image_ids = request.data.get('image_ids', [])
        if not image_ids:
            return Response({'error': 'No image IDs provided'}, status=status.HTTP_400_BAD_REQUEST)

        deleted_count = 0
        for image_id in image_ids:
            try:
                image = EventGallery.objects.get(id=image_id)
                image.delete()
                deleted_count += 1
            except EventGallery.DoesNotExist:
                pass

        return Response({'deleted': deleted_count}, status=status.HTTP_200_OK)

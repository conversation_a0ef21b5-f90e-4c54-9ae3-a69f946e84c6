from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.shortcuts import get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.db.models import Count, Q
from datetime import datetime, timedelta
import csv
import io
from .models import Participant, ParticipantType, Attendance, VisitingInterest, ParticipantVisitingInterest
from .serializers import (
    ParticipantSerializer, ParticipantListSerializer,
    ParticipantRegistrationSerializer, ParticipantTypeSerializer,
    AttendanceSerializer, VisitingInterestSerializer, ParticipantVisitingInterestSerializer
)
from events.email_service import get_email_service


class ParticipantTypeViewSet(viewsets.ModelViewSet):
    queryset = ParticipantType.objects.all()
    serializer_class = ParticipantTypeSerializer

    def get_permissions(self):
        """Allow public access for list and retrieve actions"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]


class VisitingInterestViewSet(viewsets.ModelViewSet):
    queryset = VisitingInterest.objects.all()
    serializer_class = VisitingInterestSerializer

    def get_permissions(self):
        """Allow public access for list and retrieve actions"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        event_id = self.request.query_params.get('event', None)

        if event_id is not None:
            queryset = queryset.filter(event=event_id, is_active=True)

        return queryset.order_by('name')


class ParticipantViewSet(viewsets.ModelViewSet):
    queryset = Participant.objects.all()

    def get_permissions(self):
        """Allow public access for CRUD and assignment actions"""
        if self.action in ['create', 'list', 'retrieve', 'update', 'partial_update', 'destroy', 'confirm', 'assign_hotel', 'assign_driver', 'regenerate_badge', 'badge']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        if self.action == 'create':
            return ParticipantRegistrationSerializer
        elif self.action in ['update', 'partial_update']:
            return ParticipantRegistrationSerializer  # Use the same serializer for updates
        elif self.action == 'list':
            return ParticipantListSerializer
        return ParticipantSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        event_id = self.request.query_params.get('event', None)
        participant_type = self.request.query_params.get('type', None)

        if event_id is not None:
            queryset = queryset.filter(event=event_id)
        if participant_type is not None:
            queryset = queryset.filter(participant_type=participant_type)

        return queryset

    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """Confirm participant registration"""
        participant = self.get_object()
        participant.is_confirmed = True
        participant.save()

        serializer = self.get_serializer(participant)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def assign_hotel(self, request, pk=None):
        """Assign or remove hotel assignment for participant"""
        participant = self.get_object()
        hotel_id = request.data.get('hotel_id')

        # Handle removal of assignment
        if not hotel_id or hotel_id == '0' or hotel_id == 0:
            participant.assigned_hotel = None
            participant.save()
            serializer = self.get_serializer(participant)
            return Response(serializer.data)

        try:
            from hotels.models import Hotel
            hotel = Hotel.objects.get(id=hotel_id)
            participant.assigned_hotel = hotel
            participant.save()

            serializer = self.get_serializer(participant)
            return Response(serializer.data)
        except Hotel.DoesNotExist:
            return Response({'error': 'Hotel not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def assign_driver(self, request, pk=None):
        """Assign or remove driver assignment for participant"""
        participant = self.get_object()
        driver_id = request.data.get('driver_id')

        # Handle removal of assignment
        if not driver_id or driver_id == '0' or driver_id == 0:
            participant.assigned_driver = None
            participant.save()
            serializer = self.get_serializer(participant)
            return Response(serializer.data)

        try:
            from drivers.models import Driver
            driver = Driver.objects.get(id=driver_id)
            participant.assigned_driver = driver
            participant.save()

            serializer = self.get_serializer(participant)
            return Response(serializer.data)
        except Driver.DoesNotExist:
            return Response({'error': 'Driver not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['get'])
    def badge(self, request, pk=None):
        """Get participant badge"""
        participant = self.get_object()
        if hasattr(participant, 'badge'):
            badge = participant.badge
            if badge.badge_image:
                return Response({
                    'badge_url': request.build_absolute_uri(badge.badge_image.url),
                    'qr_code_url': request.build_absolute_uri(badge.qr_code_image.url) if badge.qr_code_image else None,
                    'generated_at': badge.generated_at
                })
        return Response({'error': 'Badge not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def regenerate_badge(self, request, pk=None):
        """Regenerate participant badge"""
        participant = self.get_object()
        if hasattr(participant, 'badge'):
            badge = participant.badge
            try:
                badge.generate_badge()
                return Response({
                    'message': 'Badge regenerated successfully',
                    'badge_url': request.build_absolute_uri(badge.badge_image.url)
                })
            except Exception as e:
                return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response({'error': 'Badge not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def verify(self, request):
        """Verify participant by UUID (for QR code scanning)"""
        uuid = request.query_params.get('uuid')
        if not uuid:
            return Response({'error': 'UUID required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            participant = Participant.objects.get(uuid=uuid)
            serializer = ParticipantListSerializer(participant)
            return Response(serializer.data)
        except Participant.DoesNotExist:
            return Response({'error': 'Participant not found'}, status=status.HTTP_404_NOT_FOUND)


class AttendanceViewSet(viewsets.ModelViewSet):
    queryset = Attendance.objects.all()
    serializer_class = AttendanceSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        participant_id = self.request.query_params.get('participant', None)
        event_schedule_id = self.request.query_params.get('event_schedule', None)

        if participant_id is not None:
            queryset = queryset.filter(participant=participant_id)
        if event_schedule_id is not None:
            queryset = queryset.filter(event_schedule=event_schedule_id)

        return queryset

    @action(detail=False, methods=['post'])
    def check_in(self, request):
        """Check in participant using QR code"""
        uuid = request.data.get('uuid')
        event_schedule_id = request.data.get('event_schedule_id')
        checked_in_by = request.data.get('checked_in_by', '')
        notes = request.data.get('notes', '')

        if not uuid or not event_schedule_id:
            return Response({'error': 'UUID and event_schedule_id required'},
                          status=status.HTTP_400_BAD_REQUEST)

        try:
            participant = Participant.objects.get(uuid=uuid)
            from events.models import EventSchedule
            event_schedule = EventSchedule.objects.get(id=event_schedule_id)

            # Check if already checked in
            if Attendance.objects.filter(participant=participant, event_schedule=event_schedule).exists():
                return Response({'error': 'Already checked in'},
                              status=status.HTTP_400_BAD_REQUEST)

            # Create attendance record
            attendance = Attendance.objects.create(
                participant=participant,
                event_schedule=event_schedule,
                checked_in_by=checked_in_by,
                notes=notes
            )

            serializer = AttendanceSerializer(attendance)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Participant.DoesNotExist:
            return Response({'error': 'Participant not found'},
                          status=status.HTTP_404_NOT_FOUND)
        except EventSchedule.DoesNotExist:
            return Response({'error': 'Event schedule not found'},
                          status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def daily_report(self, request):
        """Generate daily attendance report"""
        date_str = request.query_params.get('date')
        event_id = request.query_params.get('event')

        if not date_str:
            date_str = timezone.now().date().strftime('%Y-%m-%d')

        try:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response({'error': 'Invalid date format. Use YYYY-MM-DD'},
                          status=status.HTTP_400_BAD_REQUEST)

        # Filter attendance records
        queryset = Attendance.objects.filter(checked_in_at__date=report_date)

        if event_id:
            queryset = queryset.filter(event_schedule__event_id=event_id)

        # Get attendance statistics
        total_attendance = queryset.count()
        unique_participants = queryset.values('participant').distinct().count()

        # Group by event schedule
        schedule_stats = queryset.values(
            'event_schedule__title',
            'event_schedule__start_time',
            'event_schedule__event__name'
        ).annotate(
            attendance_count=Count('id')
        ).order_by('event_schedule__start_time')

        # Group by participant type
        type_stats = queryset.values(
            'participant__participant_type__name',
            'participant__participant_type__color'
        ).annotate(
            attendance_count=Count('id')
        ).order_by('participant__participant_type__name')

        return Response({
            'date': date_str,
            'total_attendance': total_attendance,
            'unique_participants': unique_participants,
            'schedule_breakdown': list(schedule_stats),
            'participant_type_breakdown': list(type_stats)
        })

    @action(detail=False, methods=['get'])
    def export_daily_report(self, request):
        """Export daily attendance report as CSV"""
        date_str = request.query_params.get('date')
        event_id = request.query_params.get('event')

        if not date_str:
            date_str = timezone.now().date().strftime('%Y-%m-%d')

        try:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response({'error': 'Invalid date format. Use YYYY-MM-DD'},
                          status=status.HTTP_400_BAD_REQUEST)

        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="attendance_report_{date_str}.csv"'

        writer = csv.writer(response)

        # Write header
        writer.writerow([
            'Participant Name', 'Email', 'Institution', 'Participant Type',
            'Event', 'Session', 'Check-in Time', 'Checked In By', 'Notes'
        ])

        # Filter attendance records
        queryset = Attendance.objects.filter(checked_in_at__date=report_date).select_related(
            'participant', 'participant__participant_type', 'event_schedule', 'event_schedule__event'
        )

        if event_id:
            queryset = queryset.filter(event_schedule__event_id=event_id)

        # Write data rows
        for attendance in queryset:
            writer.writerow([
                attendance.participant.full_name,
                attendance.participant.email,
                attendance.participant.institution_name,
                attendance.participant.participant_type.name,
                attendance.event_schedule.event.name,
                attendance.event_schedule.title,
                attendance.checked_in_at.strftime('%Y-%m-%d %H:%M:%S'),
                attendance.checked_in_by,
                attendance.notes
            ])

        return response


@api_view(['GET'])
@permission_classes([AllowAny])
def check_email_exists(request):
    """Check if an email is already registered for any event"""
    email = request.GET.get('email', '').strip().lower()

    if not email:
        return JsonResponse({'error': 'Email parameter is required'}, status=400)

    # Check if email exists in any participant record
    exists = Participant.objects.filter(email__iexact=email).exists()

    return JsonResponse({
        'exists': exists,
        'email': email
    })

import React, { useState } from 'react';
import { Row, Col, Card, Modal, Button, Badge } from 'react-bootstrap';
import { EventGallery, getMediaUrl } from '../services/api';

interface DailyGalleryProps {
  galleries: EventGallery[];
  eventName: string;
}

const DailyGallery: React.FC<DailyGalleryProps> = ({ galleries, eventName }) => {
  const [selectedImage, setSelectedImage] = useState<EventGallery | null>(null);
  const [showModal, setShowModal] = useState(false);

  // Group galleries by date
  const groupGalleriesByDate = (galleries: EventGallery[]) => {
    const grouped: { [key: string]: EventGallery[] } = {};
    
    galleries.forEach(gallery => {
      const date = new Date(gallery.uploaded_at).toDateString();
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(gallery);
    });
    
    return grouped;
  };

  const handleImageClick = (image: EventGallery) => {
    setSelectedImage(image);
    setShowModal(true);
  };

  const handleDownload = (image: EventGallery) => {
    const link = document.createElement('a');
    link.href = getMediaUrl(image.image);
    link.download = `${eventName}_${image.title.replace(/\s+/g, '_')}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const groupedGalleries = groupGalleriesByDate(galleries);

  if (galleries.length === 0) {
    return (
      <div className="text-center py-5">
        <i className="fas fa-images text-muted" style={{ fontSize: '4rem' }}></i>
        <h4 className="mt-3 text-muted">No Photos Available</h4>
        <p className="text-muted">Event photos will be uploaded here during and after the event.</p>
      </div>
    );
  }

  return (
    <div>
      {Object.entries(groupedGalleries).map(([date, dayGalleries]) => (
        <div key={date} className="mb-5">
          <div className="d-flex justify-content-between align-items-center mb-3">
            <h4 className="text-primary fw-bold">
              <i className="fas fa-calendar-day me-2"></i>
              {new Date(date).toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </h4>
            <Badge bg="primary" className="fs-6">
              {dayGalleries.length} photos
            </Badge>
          </div>
          
          <Row>
            {dayGalleries.map((image) => (
              <Col lg={3} md={4} sm={6} key={image.id} className="mb-4">
                <Card className="h-100 shadow-sm hover-shadow gallery-card">
                  <div 
                    className="position-relative overflow-hidden gallery-image-container"
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleImageClick(image)}
                  >
                    <Card.Img 
                      variant="top" 
                      src={getMediaUrl(image.image)} 
                      alt={image.title}
                      className="gallery-image"
                      style={{ 
                        height: '200px', 
                        objectFit: 'cover',
                        transition: 'transform 0.3s ease'
                      }}
                    />
                    {image.is_featured && (
                      <Badge 
                        bg="warning" 
                        className="position-absolute top-0 start-0 m-2"
                      >
                        <i className="fas fa-star me-1"></i>
                        Featured
                      </Badge>
                    )}
                    <div className="gallery-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                      <div className="text-white text-center">
                        <i className="fas fa-search-plus fa-2x mb-2"></i>
                        <p className="mb-0 small">Click to view</p>
                      </div>
                    </div>
                    <div className="position-absolute top-0 end-0 m-2">
                      <Button
                        variant="light"
                        size="sm"
                        className="rounded-circle opacity-75"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownload(image);
                        }}
                        title="Download image"
                      >
                        <i className="fas fa-download"></i>
                      </Button>
                    </div>
                  </div>
                  
                  <Card.Body className="p-3">
                    <Card.Title className="h6 mb-2">{image.title}</Card.Title>
                    {image.description && (
                      <Card.Text className="text-muted small mb-2">
                        {image.description.length > 80 
                          ? `${image.description.substring(0, 80)}...` 
                          : image.description}
                      </Card.Text>
                    )}
                    <div className="d-flex justify-content-between align-items-center">
                      <small className="text-muted">
                        <i className="fas fa-clock me-1"></i>
                        {new Date(image.uploaded_at).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </small>
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => handleImageClick(image)}
                      >
                        <i className="fas fa-eye me-1"></i>
                        View
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      ))}

      {/* Image Modal */}
      <Modal 
        show={showModal} 
        onHide={() => setShowModal(false)} 
        size="lg" 
        centered
      >
        <Modal.Header closeButton className="border-0">
          <Modal.Title>
            {selectedImage?.title}
            {selectedImage?.is_featured && (
              <Badge bg="warning" className="ms-2">
                <i className="fas fa-star me-1"></i>
                Featured
              </Badge>
            )}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="text-center p-0">
          {selectedImage && (
            <>
              <img 
                src={getMediaUrl(selectedImage.image)} 
                alt={selectedImage.title}
                className="img-fluid"
                style={{ maxHeight: '70vh', width: '100%', objectFit: 'contain' }}
              />
              {selectedImage.description && (
                <div className="p-4">
                  <p className="text-muted mb-0">{selectedImage.description}</p>
                </div>
              )}
            </>
          )}
        </Modal.Body>
        <Modal.Footer className="border-0">
          <div className="w-100 d-flex justify-content-between align-items-center">
            <small className="text-muted">
              {selectedImage && (
                <>
                  <i className="fas fa-clock me-1"></i>
                  {new Date(selectedImage.uploaded_at).toLocaleString()}
                </>
              )}
            </small>
            <div>
              <Button 
                variant="secondary" 
                onClick={() => setShowModal(false)}
                className="me-2"
              >
                Close
              </Button>
              {selectedImage && (
                <Button 
                  variant="primary" 
                  onClick={() => handleDownload(selectedImage)}
                >
                  <i className="fas fa-download me-2"></i>
                  Download
                </Button>
              )}
            </div>
          </div>
        </Modal.Footer>
      </Modal>

      {/* Custom Styles */}
      <style>{`
        .gallery-card {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .gallery-card:hover {
          transform: translateY(-5px);
        }
        
        .gallery-image-container {
          position: relative;
        }
        
        .gallery-overlay {
          background: rgba(0, 0, 0, 0.7);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        .gallery-card:hover .gallery-overlay {
          opacity: 1;
        }
        
        .gallery-card:hover .gallery-image {
          transform: scale(1.1);
        }
      `}</style>
    </div>
  );
};

export default DailyGallery;

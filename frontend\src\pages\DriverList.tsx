import React, { useState, useEffect } from 'react';
import { Container, <PERSON>, Col, Card, Button, Badge, Form, InputGroup, <PERSON><PERSON>, Spin<PERSON>, Modal } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { driverService, Driver, getMediaUrl } from '../services/api';

const DriverList: React.FC = () => {
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAvailable, setFilterAvailable] = useState<boolean | undefined>(undefined);
  const [showToggleModal, setShowToggleModal] = useState(false);
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [showImportModal, setShowImportModal] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importLoading, setImportLoading] = useState(false);
  const [importResult, setImportResult] = useState<any>(null);

  useEffect(() => {
    fetchDrivers();
  }, [filterAvailable]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchDrivers = async () => {
    try {
      setLoading(true);
      const response = await driverService.getDrivers(undefined, filterAvailable);
      const driversData = Array.isArray(response.data) ? response.data : 
                         ((response.data as any)?.results ? (response.data as any).results : []);
      setDrivers(driversData);
      setError(null);
    } catch (err) {
      console.error('Error fetching drivers:', err);
      setError('Failed to load drivers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filteredDrivers = drivers.filter(driver =>
    driver.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    driver.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
    driver.car_plate.toLowerCase().includes(searchTerm.toLowerCase()) ||
    driver.car_code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleToggleAvailability = async (driver: Driver) => {
    try {
      await driverService.toggleAvailability(driver.id);
      await fetchDrivers(); // Refresh the list
      setShowToggleModal(false);
      setSelectedDriver(null);
    } catch (err) {
      console.error('Error toggling driver availability:', err);
      setError('Failed to update driver availability. Please try again.');
    }
  };

  const handleExport = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/drivers/export_csv/');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'drivers_export.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error exporting drivers:', err);
      setError('Failed to export drivers. Please try again.');
    }
  };

  const handleDownloadSample = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/drivers/download_sample/');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'drivers_sample.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error downloading sample:', err);
      setError('Failed to download sample. Please try again.');
    }
  };

  const handleImport = async () => {
    if (!importFile) {
      setError('Please select a file to import.');
      return;
    }

    try {
      setImportLoading(true);
      const formData = new FormData();
      formData.append('file', importFile);

      const response = await fetch('http://localhost:8000/api/drivers/import_csv/', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      setImportResult(result);

      if (response.ok) {
        await fetchDrivers(); // Refresh the list
        setImportFile(null);
      }
    } catch (err) {
      console.error('Error importing drivers:', err);
      setError('Failed to import drivers. Please try again.');
    } finally {
      setImportLoading(false);
    }
  };

  const openToggleModal = (driver: Driver) => {
    setSelectedDriver(driver);
    setShowToggleModal(true);
  };

  if (loading) {
    return (
      <Container className="text-center mt-5">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-3">Loading drivers...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h1 className="display-5 fw-bold text-primary">
                <i className="fas fa-car me-3"></i>
                Driver Management
              </h1>
              <p className="lead text-muted">Manage transportation drivers for event participants</p>
            </div>
            <div className="d-flex gap-2">
              <Button variant="outline-success" onClick={handleExport}>
                <i className="fas fa-download me-2"></i>
                Export CSV
              </Button>
              <Button variant="outline-info" onClick={handleDownloadSample}>
                <i className="fas fa-file-csv me-2"></i>
                Sample CSV
              </Button>
              <Button variant="outline-warning" onClick={() => setShowImportModal(true)}>
                <i className="fas fa-upload me-2"></i>
                Import CSV
              </Button>
              <Link to="/drivers/new" className="btn btn-primary btn-lg">
                <i className="fas fa-plus me-2"></i>
                Add New Driver
              </Link>
            </div>
          </div>

          {/* Search and Filter Controls */}
          <Row className="mb-4">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="fas fa-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Search drivers by name, phone, car plate, or car code..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
            <Col md={6}>
              <Form.Select
                value={filterAvailable === undefined ? '' : filterAvailable.toString()}
                onChange={(e) => {
                  const value = e.target.value;
                  setFilterAvailable(value === '' ? undefined : value === 'true');
                }}
              >
                <option value="">All Drivers</option>
                <option value="true">Available Drivers</option>
                <option value="false">Unavailable Drivers</option>
              </Form.Select>
            </Col>
          </Row>

          {error && (
            <Alert variant="danger" className="mb-4">
              <i className="fas fa-exclamation-triangle me-2"></i>
              {error}
            </Alert>
          )}
        </Col>
      </Row>

      {/* Drivers Grid */}
      <Row>
        {filteredDrivers.length === 0 ? (
          <Col>
            <Card className="text-center py-5">
              <Card.Body>
                <i className="fas fa-car fa-4x text-muted mb-3"></i>
                <h4 className="text-muted">No Drivers Found</h4>
                <p className="text-muted">
                  {searchTerm ? 'No drivers match your search criteria.' : 'No drivers have been added yet.'}
                </p>
                <Link to="/drivers/new" className="btn btn-primary">
                  <i className="fas fa-plus me-2"></i>
                  Add First Driver
                </Link>
              </Card.Body>
            </Card>
          </Col>
        ) : (
          filteredDrivers.map((driver) => (
            <Col lg={4} md={6} key={driver.id} className="mb-4">
              <Card className="h-100 shadow-sm driver-card">
                <Card.Header className="bg-primary text-white">
                  <div className="d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">{driver.name}</h5>
                    <Badge bg={driver.is_available ? 'success' : 'warning'}>
                      {driver.is_available ? 'Available' : 'Busy'}
                    </Badge>
                  </div>
                </Card.Header>
                
                <Card.Body className="d-flex flex-column">
                  <div className="text-center mb-3">
                    {driver.photo ? (
                      <img 
                        src={getMediaUrl(driver.photo)} 
                        alt={driver.name}
                        className="rounded-circle"
                        style={{ width: '80px', height: '80px', objectFit: 'cover' }}
                      />
                    ) : (
                      <div 
                        className="rounded-circle bg-secondary d-flex align-items-center justify-content-center mx-auto"
                        style={{ width: '80px', height: '80px' }}
                      >
                        <i className="fas fa-user fa-2x text-white"></i>
                      </div>
                    )}
                  </div>

                  <div className="mb-3">
                    <p className="text-muted mb-2">
                      <i className="fas fa-phone me-2"></i>
                      {driver.phone}
                    </p>
                    
                    <p className="text-muted mb-2">
                      <i className="fas fa-envelope me-2"></i>
                      {driver.email}
                    </p>
                    
                    <hr />
                    
                    <div className="car-info bg-light p-3 rounded">
                      <h6 className="fw-bold mb-2">
                        <i className="fas fa-car me-2"></i>
                        Vehicle Information
                      </h6>
                      <p className="mb-1"><strong>Plate:</strong> {driver.car_plate}</p>
                      <p className="mb-1"><strong>Code:</strong> {driver.car_code}</p>
                      {driver.car_model && (
                        <p className="mb-1"><strong>Model:</strong> {driver.car_model}</p>
                      )}
                      {driver.car_color && (
                        <p className="mb-1"><strong>Color:</strong> {driver.car_color}</p>
                      )}
                      {driver.license_number && (
                        <p className="mb-0"><strong>License:</strong> {driver.license_number}</p>
                      )}
                    </div>

                    {driver.notes && (
                      <div className="mt-3">
                        <h6 className="fw-bold">Notes:</h6>
                        <p className="text-muted small">
                          {driver.notes.length > 100 
                            ? `${driver.notes.substring(0, 100)}...` 
                            : driver.notes}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="mt-auto">
                    <div className="mb-3">
                      <small className="text-muted">
                        <i className="fas fa-calendar me-1"></i>
                        Added: {new Date(driver.created_at).toLocaleDateString()}
                      </small>
                    </div>

                    <div className="d-grid gap-2">
                      <Link 
                        to={`/drivers/${driver.id}`} 
                        className="btn btn-primary"
                      >
                        <i className="fas fa-eye me-2"></i>
                        View Details
                      </Link>
                      
                      <div className="btn-group">
                        <Link 
                          to={`/drivers/${driver.id}/assignments`} 
                          className="btn btn-outline-secondary btn-sm"
                        >
                          <i className="fas fa-route me-1"></i>
                          Assignments
                        </Link>
                        <Button 
                          variant={driver.is_available ? 'outline-warning' : 'outline-success'}
                          size="sm"
                          onClick={() => openToggleModal(driver)}
                        >
                          <i className={`fas ${driver.is_available ? 'fa-pause' : 'fa-play'} me-1`}></i>
                          {driver.is_available ? 'Set Busy' : 'Set Available'}
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))
        )}
      </Row>

      {/* Statistics */}
      {drivers.length > 0 && (
        <Row className="mt-5">
          <Col>
            <Card className="bg-light">
              <Card.Body>
                <Row className="text-center">
                  <Col md={3}>
                    <h4 className="text-primary">{drivers.length}</h4>
                    <p className="text-muted mb-0">Total Drivers</p>
                  </Col>
                  <Col md={3}>
                    <h4 className="text-success">{drivers.filter(d => d.is_available).length}</h4>
                    <p className="text-muted mb-0">Available Drivers</p>
                  </Col>
                  <Col md={3}>
                    <h4 className="text-warning">{drivers.filter(d => !d.is_available).length}</h4>
                    <p className="text-muted mb-0">Busy Drivers</p>
                  </Col>
                  <Col md={3}>
                    <h4 className="text-info">{drivers.filter(d => d.photo).length}</h4>
                    <p className="text-muted mb-0">With Photos</p>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Toggle Availability Modal */}
      <Modal show={showToggleModal} onHide={() => setShowToggleModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-exchange-alt me-2"></i>
            Toggle Driver Availability
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedDriver && (
            <div>
              <p>
                Are you sure you want to change the availability status of <strong>{selectedDriver.name}</strong>?
              </p>
              <div className="alert alert-info">
                <i className="fas fa-info-circle me-2"></i>
                Current status: <Badge bg={selectedDriver.is_available ? 'success' : 'warning'}>
                  {selectedDriver.is_available ? 'Available' : 'Busy'}
                </Badge>
                <br />
                New status: <Badge bg={!selectedDriver.is_available ? 'success' : 'warning'}>
                  {!selectedDriver.is_available ? 'Available' : 'Busy'}
                </Badge>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowToggleModal(false)}>
            Cancel
          </Button>
          <Button 
            variant="primary" 
            onClick={() => selectedDriver && handleToggleAvailability(selectedDriver)}
          >
            <i className="fas fa-check me-2"></i>
            Confirm Change
          </Button>
        </Modal.Footer>
      </Modal>

      <style>{`
        .driver-card {
          transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        
        .driver-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }
        
        .car-info {
          border-left: 4px solid #007bff;
        }
        
        .btn-group .btn {
          flex: 1;
        }
      `}</style>

      {/* Import Modal */}
      <Modal show={showImportModal} onHide={() => setShowImportModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-upload me-2"></i>
            Import Drivers from CSV
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-3">
            <p className="text-muted">
              Upload a CSV file to import multiple drivers at once.
              <Button
                variant="link"
                className="p-0 ms-1"
                onClick={handleDownloadSample}
              >
                Download sample CSV
              </Button> to see the required format.
            </p>
          </div>

          <Form.Group className="mb-3">
            <Form.Label>Select CSV File</Form.Label>
            <Form.Control
              type="file"
              accept=".csv"
              onChange={(e) => {
                const file = (e.target as HTMLInputElement).files?.[0];
                setImportFile(file || null);
                setImportResult(null);
              }}
            />
          </Form.Group>

          {importResult && (
            <Alert variant={importResult.errors?.length > 0 ? "warning" : "success"}>
              <h6>{importResult.message}</h6>
              {importResult.errors?.length > 0 && (
                <div>
                  <strong>Errors:</strong>
                  <ul className="mb-0 mt-2">
                    {importResult.errors.map((error: string, index: number) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </Alert>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowImportModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleImport}
            disabled={!importFile || importLoading}
          >
            {importLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Importing...
              </>
            ) : (
              <>
                <i className="fas fa-upload me-2"></i>
                Import Drivers
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default DriverList;

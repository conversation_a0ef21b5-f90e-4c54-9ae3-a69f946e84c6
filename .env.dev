# Development Environment Configuration

# Django Settings
SECRET_KEY=django-insecure-development-key-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Settings
POSTGRES_DB=event_management_dev
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Frontend Settings
REACT_APP_API_BASE_URL=http://localhost:8000/api
REACT_APP_BACKEND_URL=http://localhost:8000

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# QR Code Settings
QR_CODE_BASE_URL=http://localhost:8000

# Email Settings
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_USE_SSL=False
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=University of Gondar Events <<EMAIL>>

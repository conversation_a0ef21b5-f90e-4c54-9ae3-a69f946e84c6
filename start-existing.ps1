# Start Existing Containers - No Build Required
# Use this when containers are already built and you just want to start them

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Starting UoG Event Management System" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$projectPath = "C:\Users\<USER>\Desktop\uog event"
Set-Location $projectPath

Write-Host "Step 1: Starting all services..." -ForegroundColor Yellow
docker-compose up -d

Write-Host "Step 2: Waiting for services..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

Write-Host "Step 3: Testing backend API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/events/" -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Backend API is ready! (HTTP $($response.StatusCode))" -ForegroundColor Green
}
catch {
    Write-Host "⚠ Backend API not ready yet, but continuing..." -ForegroundColor Yellow
}

Write-Host "Step 4: Starting frontend..." -ForegroundColor Yellow
Set-Location "$projectPath\frontend"
Start-Process cmd -ArgumentList "/k", "npm start"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "✅ System Started!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Access Points:" -ForegroundColor White
Write-Host "- Frontend: " -NoNewline; Write-Host "http://localhost:3001" -ForegroundColor Blue
Write-Host "- Backend API: " -NoNewline; Write-Host "http://localhost:8000" -ForegroundColor Blue
Write-Host "- Admin Panel: " -NoNewline; Write-Host "http://localhost:8000/admin/" -ForegroundColor Blue
Write-Host ""
Write-Host "🆕 New Management Features:" -ForegroundColor White
Write-Host "- Hotels: " -NoNewline; Write-Host "http://localhost:3001/hotels" -ForegroundColor Blue
Write-Host "- Drivers: " -NoNewline; Write-Host "http://localhost:3001/drivers" -ForegroundColor Blue
Write-Host "- Participant Types: " -NoNewline; Write-Host "http://localhost:3001/participant-types" -ForegroundColor Blue
Write-Host ""

# Check container status
Write-Host "📊 Container Status:" -ForegroundColor White
docker-compose ps

# Wait a bit then open browser
Start-Sleep -Seconds 3
Start-Process "http://localhost:3001"

Write-Host ""
Write-Host "🎉 Application is ready! Browser opened." -ForegroundColor Green
Write-Host ""
Write-Host "💡 Useful Commands:" -ForegroundColor White
Write-Host "- Stop: " -NoNewline; Write-Host "docker-compose down" -ForegroundColor Yellow
Write-Host "- Logs: " -NoNewline; Write-Host "docker-compose logs -f" -ForegroundColor Yellow
Write-Host "- Restart: " -NoNewline; Write-Host "docker-compose restart" -ForegroundColor Yellow
Write-Host ""

Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

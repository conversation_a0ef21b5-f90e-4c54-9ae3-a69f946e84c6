@echo off
REM Setup script for automated email tasks on Windows
REM This script provides instructions for setting up Windows Task Scheduler

echo Setting up automated email tasks for Windows...
echo.

set BACKEND_DIR=%cd%
set PYTHON_PATH=python
set MANAGE_PY=%BACKEND_DIR%\manage.py

echo Backend directory: %BA<PERSON><PERSON><PERSON>_DIR%
echo Python command: %PYTHON_PATH%
echo Manage.py path: %MANAGE_PY%
echo.

REM Check if manage.py exists
if not exist "%MANAGE_PY%" (
    echo Error: manage.py not found in current directory
    echo Please run this script from the Django backend directory
    pause
    exit /b 1
)

echo Creating Windows Task Scheduler commands...
echo.

REM Create a batch file for daily gallery emails
echo @echo off > send_daily_gallery.bat
echo cd /d "%BACKEND_DIR%" >> send_daily_gallery.bat
echo %PYTHON_PATH% manage.py send_daily_gallery >> send_daily_gallery.bat

REM Create a batch file for event reminders (1 day)
echo @echo off > send_reminders_1day.bat
echo cd /d "%BACKEND_DIR%" >> send_reminders_1day.bat
echo %PYTHON_PATH% manage.py send_event_reminders --days-before 1 >> send_reminders_1day.bat

REM Create a batch file for event reminders (3 days)
echo @echo off > send_reminders_3days.bat
echo cd /d "%BACKEND_DIR%" >> send_reminders_3days.bat
echo %PYTHON_PATH% manage.py send_event_reminders --days-before 3 >> send_reminders_3days.bat

REM Create a batch file for email cleanup
echo @echo off > cleanup_email_logs.bat
echo cd /d "%BACKEND_DIR%" >> cleanup_email_logs.bat
echo %PYTHON_PATH% manage.py shell -c "from events.models import EmailLog; from django.utils import timezone; from datetime import timedelta; EmailLog.objects.filter(created_at__lt=timezone.now() - timedelta(days=30)).delete()" >> cleanup_email_logs.bat

echo Batch files created successfully!
echo.
echo The following batch files have been created:
echo 1. send_daily_gallery.bat - Send daily gallery emails
echo 2. send_reminders_1day.bat - Send event reminders (1 day before)
echo 3. send_reminders_3days.bat - Send event reminders (3 days before)
echo 4. cleanup_email_logs.bat - Clean up old email logs
echo.

echo To set up automated tasks in Windows Task Scheduler:
echo.
echo 1. Open Task Scheduler (taskschd.msc)
echo 2. Click "Create Basic Task" in the Actions panel
echo 3. Set up the following tasks:
echo.
echo    DAILY GALLERY EMAILS:
echo    - Name: UoG Daily Gallery Emails
echo    - Trigger: Daily at 8:00 PM
echo    - Action: Start a program
echo    - Program: %BACKEND_DIR%\send_daily_gallery.bat
echo.
echo    EVENT REMINDERS (1 DAY):
echo    - Name: UoG Event Reminders 1 Day
echo    - Trigger: Daily at 9:00 AM
echo    - Action: Start a program
echo    - Program: %BACKEND_DIR%\send_reminders_1day.bat
echo.
echo    EVENT REMINDERS (3 DAYS):
echo    - Name: UoG Event Reminders 3 Days
echo    - Trigger: Daily at 9:00 AM
echo    - Action: Start a program
echo    - Program: %BACKEND_DIR%\send_reminders_3days.bat
echo.
echo    EMAIL LOG CLEANUP:
echo    - Name: UoG Email Log Cleanup
echo    - Trigger: Weekly on Sunday at 2:00 AM
echo    - Action: Start a program
echo    - Program: %BACKEND_DIR%\cleanup_email_logs.bat
echo.

echo You can test the batch files manually by double-clicking them or running:
echo   send_daily_gallery.bat
echo   send_reminders_1day.bat
echo   send_reminders_3days.bat
echo   cleanup_email_logs.bat
echo.

echo Note: Make sure your Django application is properly configured with:
echo 1. SMTP settings in your environment variables
echo 2. Active email configuration in Django admin
echo 3. Email templates created and activated
echo.

echo You can test the commands with dry-run first:
echo   python manage.py send_daily_gallery --dry-run
echo   python manage.py send_event_reminders --dry-run
echo.

pause

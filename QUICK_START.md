# University of Gondar Event Management System - Quick Start Guide

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed and running
- Git (for cloning the repository)
- PowerShell (Windows) or Bash (Linux/Mac)

### 1. Clone and Setup

```bash
git clone <repository-url>
cd uog-event
```

### 2. Local Development (Fastest Way)

#### Windows (PowerShell)
```powershell
# Quick start with default settings
.\deploy-local.ps1

# Or with rebuild
.\deploy-local.ps1 -Rebuild
```

#### Linux/Mac (Bash)
```bash
# Make scripts executable
chmod +x deploy-local.sh

# Quick start
./deploy-local.sh
```

#### Using Make (All platforms)
```bash
# Start development environment
make local-up

# View logs
make local-logs

# Stop environment
make local-down
```

### 3. Access Your Application

After deployment completes (usually 2-3 minutes):

- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:8000/api
- **Admin Panel**: http://localhost:8000/admin
- **Default Admin**: username: `admin`, password: `admin123`

### 4. Production Deployment

#### Windows (PowerShell - Run as Administrator)
```powershell
# Configure production environment
cp .env.prod.template .env.prod
# Edit .env.prod with your production settings

# Deploy to production
.\deploy-prod.ps1
```

#### Linux/Mac (Bash - Run as root/sudo)
```bash
# Configure production environment
cp .env.prod.template .env.prod
# Edit .env.prod with your production settings

# Deploy to production
sudo ./deploy-prod.sh
```

## 🔧 Configuration

### Environment Files

#### Local Development (`.env.local`)
```env
# Database
POSTGRES_DB=uog_event_local
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Email (optional for local)
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-test-password
```

#### Production (`.env.prod`)
```env
# Security
SECRET_KEY=your-super-secret-production-key-change-this
POSTGRES_PASSWORD=your-secure-database-password
REDIS_PASSWORD=your-secure-redis-password

# Domain
ALLOWED_HOSTS=event.uog.edu.et,www.event.uog.edu.et

# Email
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
```

## 🛠️ Common Commands

### Using Make (Recommended)
```bash
# Development
make local-up          # Start local environment
make local-down        # Stop local environment
make local-logs        # View logs
make local-shell       # Access Django shell
make local-migrate     # Run migrations

# Production
make prod-up           # Start production
make prod-down         # Stop production
make prod-logs         # View production logs

# Maintenance
make backup            # Create database backup
make health            # Check service health
make clean             # Clean Docker resources
```

### Using Docker Compose Directly
```bash
# Local development
docker-compose -f docker-compose.local.yml up -d
docker-compose -f docker-compose.local.yml logs -f
docker-compose -f docker-compose.local.yml down

# Production
docker-compose -f docker-compose.prod.yml up -d
docker-compose -f docker-compose.prod.yml logs -f
docker-compose -f docker-compose.prod.yml down
```

### Using PowerShell Scripts
```powershell
# Build custom images
.\build-images.ps1                    # Local images
.\build-images.ps1 -Production        # Production images
.\build-images.ps1 -Push              # Build and push to registry

# Deploy with options
.\deploy-local.ps1 -Rebuild           # Rebuild images
.\deploy-local.ps1 -Logs              # Show logs after deployment
.\deploy-prod.ps1 -Force              # Skip confirmations
.\deploy-prod.ps1 -NoBackup           # Skip backup creation
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
netstat -ano | findstr :3001
netstat -ano | findstr :8000

# Kill the process or change ports in docker-compose files
```

#### 2. Docker Not Running
```bash
# Start Docker Desktop
# Wait for Docker to fully start
docker info  # Should show Docker information
```

#### 3. Permission Denied (Linux/Mac)
```bash
# Make scripts executable
chmod +x *.sh

# Run with sudo for production
sudo ./deploy-prod.sh
```

#### 4. Database Connection Issues
```bash
# Check database logs
docker-compose -f docker-compose.local.yml logs db

# Reset database
docker-compose -f docker-compose.local.yml down -v
docker-compose -f docker-compose.local.yml up -d
```

#### 5. Frontend Build Issues
```bash
# Clear node_modules and rebuild
docker-compose -f docker-compose.local.yml down
docker-compose -f docker-compose.local.yml build --no-cache frontend
docker-compose -f docker-compose.local.yml up -d
```

### Health Checks
```bash
# Check all services
make health

# Check specific service
docker-compose -f docker-compose.local.yml ps
docker-compose -f docker-compose.local.yml exec backend python healthcheck.py
```

### View Logs
```bash
# All services
make local-logs

# Specific service
docker-compose -f docker-compose.local.yml logs -f backend
docker-compose -f docker-compose.local.yml logs -f frontend
docker-compose -f docker-compose.local.yml logs -f db
```

## 📚 Next Steps

1. **Configure Email**: Update email settings in your environment file
2. **Add SSL**: For production, add SSL certificates to `nginx/ssl/`
3. **Customize Branding**: Update organization details in Django admin
4. **Create Events**: Use the admin panel to create your first event
5. **Test Registration**: Test the complete registration flow

## 🆘 Getting Help

- Check the logs first: `make local-logs` or `make prod-logs`
- Review the full deployment guide: `DEPLOYMENT.md`
- Ensure all prerequisites are installed
- Verify environment variables are set correctly
- Check Docker Desktop is running and has sufficient resources

## 🎯 Production Checklist

Before going live:
- [ ] Change default admin password
- [ ] Configure proper email settings
- [ ] Add SSL certificates
- [ ] Set up domain DNS
- [ ] Configure firewall rules
- [ ] Test all functionality
- [ ] Set up monitoring and backups
- [ ] Review security settings

#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting University of Gondar Event Management System - Backend${NC}"

# Wait for database to be ready
echo -e "${YELLOW}Waiting for database...${NC}"
while ! nc -z db 5432; do
  sleep 1
done
echo -e "${GREEN}Database is ready!${NC}"

# Wait for Redis to be ready
echo -e "${YELLOW}Waiting for Redis...${NC}"
while ! nc -z redis 6379; do
  sleep 1
done
echo -e "${GREEN}Redis is ready!${NC}"

# Run database migrations
echo -e "${YELLOW}Running database migrations...${NC}"
python manage.py migrate --noinput

# Collect static files
echo -e "${YELLOW}Collecting static files...${NC}"
python manage.py collectstatic --noinput

# Create superuser if it doesn't exist
echo -e "${YELLOW}Creating superuser if needed...${NC}"
python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
EOF

# Load initial data if needed
echo -e "${YELLOW}Loading initial data...${NC}"
python manage.py loaddata initial_data.json 2>/dev/null || echo "No initial data to load"

# Create log files
mkdir -p /app/logs
touch /app/logs/django.log
touch /app/logs/gunicorn-access.log
touch /app/logs/gunicorn-error.log

echo -e "${GREEN}Backend initialization complete!${NC}"

# Execute the main command
exec "$@"

# Deploy UoG Events to Production Server
# PowerShell Script for Windows

param(
    [string]$ServerIP = "*************",
    [string]$Username = "administrator",
    [string]$Password = "Pa`$`$w0rd@456"
)

Write-Host "========================================" -ForegroundColor Blue
Write-Host "UoG Events - Production Deployment" -ForegroundColor Blue
Write-Host "========================================" -ForegroundColor Blue
Write-Host "Server: $ServerIP" -ForegroundColor Yellow
Write-Host "Username: $Username" -ForegroundColor Yellow
Write-Host ""

# Check if required files exist
$requiredFiles = @("server-setup.sh", ".env.prod", "docker-compose.hub.yml")
foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "❌ Required file not found: $file" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ All required files found" -ForegroundColor Green

# Create secure string for password
$SecurePassword = ConvertTo-SecureString $Password -AsPlainText -Force
$Credential = New-Object System.Management.Automation.PSCredential ($Username, $SecurePassword)

Write-Host "🔐 Connecting to server..." -ForegroundColor Blue

try {
    # Test connection first
    $testConnection = Test-NetConnection -ComputerName $ServerIP -Port 22 -InformationLevel Quiet
    if (-not $testConnection) {
        Write-Host "❌ Cannot connect to server on port 22" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ Server is reachable" -ForegroundColor Green
    
    # Instructions for manual deployment
    Write-Host ""
    Write-Host "📋 Manual Deployment Instructions:" -ForegroundColor Blue
    Write-Host "=================================" -ForegroundColor Blue
    Write-Host ""
    Write-Host "1. Connect to the server using your preferred SSH client:" -ForegroundColor Yellow
    Write-Host "   ssh administrator@$ServerIP" -ForegroundColor White
    Write-Host "   Password: $Password" -ForegroundColor White
    Write-Host ""
    Write-Host "2. Run the following commands on the server:" -ForegroundColor Yellow
    Write-Host ""
    
    # Display the server setup commands
    Write-Host "# Update system and install Docker" -ForegroundColor Green
    Write-Host "sudo apt update && sudo apt upgrade -y" -ForegroundColor White
    Write-Host "curl -fsSL https://get.docker.com -o get-docker.sh" -ForegroundColor White
    Write-Host "sudo sh get-docker.sh" -ForegroundColor White
    Write-Host "sudo usermod -aG docker `$USER" -ForegroundColor White
    Write-Host "sudo systemctl start docker && sudo systemctl enable docker" -ForegroundColor White
    Write-Host ""
    
    Write-Host "# Install Docker Compose" -ForegroundColor Green
    Write-Host "sudo curl -L `"https://github.com/docker/compose/releases/latest/download/docker-compose-`$(uname -s)-`$(uname -m)`" -o /usr/local/bin/docker-compose" -ForegroundColor White
    Write-Host "sudo chmod +x /usr/local/bin/docker-compose" -ForegroundColor White
    Write-Host ""
    
    Write-Host "# Create project directory" -ForegroundColor Green
    Write-Host "sudo mkdir -p /opt/uog-events" -ForegroundColor White
    Write-Host "sudo chown `$USER:`$USER /opt/uog-events" -ForegroundColor White
    Write-Host "cd /opt/uog-events" -ForegroundColor White
    Write-Host "mkdir -p logs/nginx ssl" -ForegroundColor White
    Write-Host ""
    
    Write-Host "# Create environment file" -ForegroundColor Green
    Write-Host "nano .env.prod" -ForegroundColor White
    Write-Host ""
    Write-Host "Copy the following content to .env.prod:" -ForegroundColor Yellow
    Write-Host "----------------------------------------" -ForegroundColor Yellow
    
    # Display environment file content
    $envContent = Get-Content ".env.prod" -Raw
    Write-Host $envContent -ForegroundColor Cyan
    
    Write-Host "----------------------------------------" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "# Create Docker Compose file" -ForegroundColor Green
    Write-Host "nano docker-compose.yml" -ForegroundColor White
    Write-Host ""
    Write-Host "Copy the following content to docker-compose.yml:" -ForegroundColor Yellow
    Write-Host "------------------------------------------------" -ForegroundColor Yellow
    
    # Display docker-compose file content
    $composeContent = Get-Content "docker-compose.hub.yml" -Raw
    Write-Host $composeContent -ForegroundColor Cyan
    
    Write-Host "------------------------------------------------" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "# Install Nginx and Certbot for SSL" -ForegroundColor Green
    Write-Host "sudo apt install -y nginx certbot python3-certbot-nginx" -ForegroundColor White
    Write-Host ""
    
    Write-Host "# Configure firewall" -ForegroundColor Green
    Write-Host "sudo ufw allow ssh" -ForegroundColor White
    Write-Host "sudo ufw allow 80/tcp" -ForegroundColor White
    Write-Host "sudo ufw allow 443/tcp" -ForegroundColor White
    Write-Host "sudo ufw --force enable" -ForegroundColor White
    Write-Host ""
    
    Write-Host "# Deploy the application" -ForegroundColor Green
    Write-Host "docker-compose up -d" -ForegroundColor White
    Write-Host ""
    
    Write-Host "# Set up SSL certificate (after DNS is configured)" -ForegroundColor Green
    Write-Host "sudo certbot --nginx -d event.uog.edu.et -d www.event.uog.edu.et" -ForegroundColor White
    Write-Host ""
    
    Write-Host "# Check deployment status" -ForegroundColor Green
    Write-Host "docker-compose ps" -ForegroundColor White
    Write-Host "docker-compose logs -f" -ForegroundColor White
    Write-Host ""
    
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "🎉 Deployment Instructions Complete!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Important Notes:" -ForegroundColor Blue
    Write-Host "• Make sure to update the SECRET_KEY, passwords, and email settings in .env.prod" -ForegroundColor Yellow
    Write-Host "• Configure DNS to point event.uog.edu.et to $ServerIP before setting up SSL" -ForegroundColor Yellow
    Write-Host "• The application will be available at https://event.uog.edu.et after SSL setup" -ForegroundColor Yellow
    Write-Host ""
    
    # Save instructions to file
    $instructionsFile = "deployment-instructions.txt"
    $instructions = @"
UoG Events Management System - Production Deployment Instructions
================================================================

Server: $ServerIP
Username: $Username
Password: $Password

1. Connect to server:
   ssh administrator@$ServerIP

2. Update system and install Docker:
   sudo apt update && sudo apt upgrade -y
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo usermod -aG docker `$USER
   sudo systemctl start docker && sudo systemctl enable docker

3. Install Docker Compose:
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-`$(uname -s)-`$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose

4. Create project directory:
   sudo mkdir -p /opt/uog-events
   sudo chown `$USER:`$USER /opt/uog-events
   cd /opt/uog-events
   mkdir -p logs/nginx ssl

5. Create .env.prod file with the content from .env.prod in this directory

6. Create docker-compose.yml file with the content from docker-compose.hub.yml in this directory

7. Install Nginx and Certbot:
   sudo apt install -y nginx certbot python3-certbot-nginx

8. Configure firewall:
   sudo ufw allow ssh
   sudo ufw allow 80/tcp
   sudo ufw allow 443/tcp
   sudo ufw --force enable

9. Deploy application:
   docker-compose up -d

10. Set up SSL (after DNS configuration):
    sudo certbot --nginx -d event.uog.edu.et -d www.event.uog.edu.et

11. Check deployment:
    docker-compose ps
    docker-compose logs -f

Important:
- Update SECRET_KEY, passwords, and email settings in .env.prod
- Configure DNS to point event.uog.edu.et to $ServerIP
- Application will be available at https://event.uog.edu.et
"@
    
    $instructions | Out-File -FilePath $instructionsFile -Encoding UTF8
    Write-Host "📄 Instructions saved to: $instructionsFile" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🚀 Ready for manual deployment!" -ForegroundColor Green

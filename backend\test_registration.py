#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'event_management.settings')
django.setup()

from participants.models import Participant, ParticipantType
from events.models import Event
from events.email_service import get_email_service

def test_registration_email():
    print("🧪 Testing Registration Email Process...")
    
    # Get or create test data
    try:
        event = Event.objects.first()
        if not event:
            print("❌ No events found. Please create an event first.")
            return
        
        participant_type = ParticipantType.objects.first()
        if not participant_type:
            print("❌ No participant types found. Please create a participant type first.")
            return
        
        print(f"✓ Using event: {event.name}")
        print(f"✓ Using participant type: {participant_type.name}")
        
        # Create test participant
        test_email = "<EMAIL>"
        
        # Delete existing test participant if exists
        Participant.objects.filter(email=test_email).delete()
        
        from django.utils import timezone
        from datetime import timedelta

        participant = Participant.objects.create(
            first_name="Test",
            last_name="Participant",
            email=test_email,
            phone="+251-911-123456",
            institution_name="Test University",
            position="Test Position",
            participant_type=participant_type,
            event=event,
            arrival_date=timezone.now() + timedelta(days=1),
            departure_date=timezone.now() + timedelta(days=3),
            profile_photo="",  # Empty string for required field
        )
        
        print(f"✓ Created test participant: {participant.full_name}")
        print(f"✓ Email: {participant.email}")
        
        # Test email service
        email_service = get_email_service()
        if not email_service.config:
            print("❌ No active email configuration found")
            return
        
        print(f"✓ Email service configured: {email_service.config.name}")
        
        # Send registration confirmation
        print("📧 Sending registration confirmation email...")
        success = email_service.send_registration_confirmation(participant)
        
        if success:
            print("✅ Registration confirmation email sent successfully!")
        else:
            print("❌ Failed to send registration confirmation email")
        
        # Check email logs
        from events.models import EmailLog
        recent_log = EmailLog.objects.filter(
            recipient_email=test_email,
            template_type='registration_confirmation'
        ).order_by('-created_at').first()
        
        if recent_log:
            print(f"📋 Email log status: {recent_log.status}")
            if recent_log.error_message:
                print(f"❌ Error: {recent_log.error_message}")
        else:
            print("⚠️ No email log found")
        
        # Clean up
        participant.delete()
        print("🧹 Cleaned up test participant")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_registration_email()

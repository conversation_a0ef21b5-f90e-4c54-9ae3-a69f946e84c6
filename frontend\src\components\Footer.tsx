import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useBranding } from '../hooks/useBranding';
import { developerService, Developer } from '../services/api';

const Footer: React.FC = () => {
  const { organization } = useBranding();
  const [developers, setDevelopers] = useState<Developer[]>([]);

  useEffect(() => {
    const loadDevelopers = async () => {
      try {
        const response = await developerService.getDevelopers();
        setDevelopers(response.data.results);
      } catch (error) {
        console.error('Failed to load developers:', error);
      }
    };

    loadDevelopers();
  }, []);

  return (
    <footer className="footer-section">
      <div className="footer-content py-5">
        <div className="section-container">
          <div className="row g-4">
            <div className="col-lg-4">
              <div className="footer-brand mb-4">
                <div className="organization-logo mb-4 text-center">
                  {organization?.logo ? (
                    <img
                      src={organization.logo}
                      alt={organization.name}
                      className="footer-logo mb-3"
                      style={{
                        maxWidth: '120px',
                        height: 'auto',
                        filter: 'brightness(0) invert(1)', // Makes logo white
                        opacity: 0.9
                      }}
                    />
                  ) : (
                    <i className="fas fa-university text-primary mb-3" style={{ fontSize: '3rem' }}></i>
                  )}
                  <h4 className="fw-bold text-white">
                    {organization?.name || 'University of Gondar'}
                  </h4>
                  <p className="text-light mb-0" style={{ fontSize: '0.9rem' }}>
                    {organization?.motto || 'Excellence in Education, Research and Community Service'}
                  </p>
                </div>
                <p className="footer-description text-light mb-3 text-center">
                  {organization?.description || 'A comprehensive platform for managing academic events, conferences, and institutional gatherings at Ethiopia\'s premier university.'}
                </p>
                <div className="footer-social d-flex gap-3 justify-content-center">
                  {organization ? (
                    <>
                      {organization.facebook_url && (
                        <a href={organization.facebook_url} target="_blank" rel="noopener noreferrer" className="social-link">
                          <i className="fab fa-facebook-f"></i>
                        </a>
                      )}
                      {organization.twitter_url && (
                        <a href={organization.twitter_url} target="_blank" rel="noopener noreferrer" className="social-link">
                          <i className="fab fa-twitter"></i>
                        </a>
                      )}
                      {organization.linkedin_url && (
                        <a href={organization.linkedin_url} target="_blank" rel="noopener noreferrer" className="social-link">
                          <i className="fab fa-linkedin-in"></i>
                        </a>
                      )}
                      {organization.instagram_url && (
                        <a href={organization.instagram_url} target="_blank" rel="noopener noreferrer" className="social-link">
                          <i className="fab fa-instagram"></i>
                        </a>
                      )}
                      {organization.website && (
                        <a href={organization.website} target="_blank" rel="noopener noreferrer" className="social-link">
                          <i className="fas fa-globe"></i>
                        </a>
                      )}
                    </>
                  ) : (
                    <>
                      <a href="https://www.uog.edu.et" target="_blank" rel="noopener noreferrer" className="social-link">
                        <i className="fas fa-globe"></i>
                      </a>
                      <a href="https://www.facebook.com/universityofgondar" target="_blank" rel="noopener noreferrer" className="social-link">
                        <i className="fab fa-facebook-f"></i>
                      </a>
                      <a href="https://twitter.com/uogondar" target="_blank" rel="noopener noreferrer" className="social-link">
                        <i className="fab fa-twitter"></i>
                      </a>
                      <a href="https://www.linkedin.com/school/university-of-gondar" target="_blank" rel="noopener noreferrer" className="social-link">
                        <i className="fab fa-linkedin-in"></i>
                      </a>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="col-lg-4">
              <h6 className="fw-bold mb-3 text-white">Quick Links</h6>
              <div className="footer-links">
                <Link to="/events" className="footer-link d-block mb-2">
                  <i className="fas fa-calendar-alt me-2"></i>
                  All Events
                </Link>
                <Link to="/participant-register" className="footer-link d-block mb-2">
                  <i className="fas fa-user-plus me-2"></i>
                  Register for Events
                </Link>
                <a href="#university" className="footer-link d-block mb-2">
                  <i className="fas fa-info-circle me-2"></i>
                  About University
                </a>
                <a href="#campus-life" className="footer-link d-block mb-2">
                  <i className="fas fa-graduation-cap me-2"></i>
                  Campus Life
                </a>
              </div>
            </div>

            <div className="col-lg-4">
              <h6 className="fw-bold mb-3 text-white">Contact Information</h6>
              <div className="contact-info">
                <div className="contact-item d-flex align-items-center mb-2">
                  <i className="fas fa-envelope text-primary me-3"></i>
                  <span className="text-light">{organization?.email || '<EMAIL>'}</span>
                </div>
                <div className="contact-item d-flex align-items-center mb-2">
                  <i className="fas fa-phone text-primary me-3"></i>
                  <span className="text-light">{organization?.phone || '+251-58-114-1240'}</span>
                </div>
                <div className="contact-item d-flex align-items-center mb-2">
                  <i className="fas fa-map-marker-alt text-primary me-3"></i>
                  <span className="text-light">{organization?.full_address || 'Gondar, Amhara Region, Ethiopia'}</span>
                </div>
                <div className="contact-item d-flex align-items-center mb-2">
                  <i className="fas fa-globe text-primary me-3"></i>
                  <a href={organization?.website || 'https://www.uog.edu.et'} target="_blank" rel="noopener noreferrer" className="footer-link">
                    {organization?.website?.replace('https://', '').replace('http://', '') || 'www.uog.edu.et'}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="footer-bottom py-3">
        <div className="section-container">
          <div className="row align-items-center">
            <div className="col-md-6">
              <p className="footer-copyright mb-0 text-white">
                © {new Date().getFullYear()} {organization?.name || 'University of Gondar'}. All rights reserved.
              </p>
            </div>
            <div className="col-md-6 text-md-end">
              <div className="d-flex align-items-center justify-content-md-end justify-content-center">
                <span className="text-white me-3">Developed by</span>
                <div className="developers-list d-flex gap-2">
                  {developers && developers.length > 0 ? developers.map((developer) => (
                    <div
                      key={developer.id}
                      className="developer-item position-relative"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      title={`${developer.full_name} - ${developer.profession}`}
                    >
                      {developer.photo_url ? (
                        <img
                          src={developer.photo_url}
                          alt={developer.full_name}
                          className="developer-photo rounded-circle"
                          style={{ width: '30px', height: '30px', objectFit: 'cover' }}
                        />
                      ) : (
                        <div
                          className="developer-avatar rounded-circle bg-primary d-flex align-items-center justify-content-center text-white fw-bold"
                          style={{ width: '30px', height: '30px', fontSize: '0.7rem' }}
                        >
                          {developer.full_name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                        </div>
                      )}
                    </div>
                  )) : (
                    <div className="text-white">
                      <small>University of Gondar Development Team</small>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

# University of Gondar Event Management System - Docker Image Build Script

param(
    [string]$Registry = "localhost:5000",
    [string]$Tag = "latest",
    [switch]$Push,
    [switch]$Production
)

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Blue = "Cyan"

Write-Host "========================================" -ForegroundColor $Blue
Write-Host "UoG Event Management - Docker Image Build" -ForegroundColor $Blue
Write-Host "========================================" -ForegroundColor $Blue

# Determine environment
$env = if ($Production) { "prod" } else { "local" }
$composeFile = if ($Production) { "docker-compose.prod.yml" } else { "docker-compose.local.yml" }

Write-Host "Building images for: $env environment" -ForegroundColor $Yellow
Write-Host "Registry: $Registry" -ForegroundColor $Yellow
Write-Host "Tag: $Tag" -ForegroundColor $Yellow

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor $Green
} catch {
    Write-Host "✗ Error: Docker is not running. Please start Docker and try again." -ForegroundColor $Red
    exit 1
}

# Build backend image
Write-Host "Building backend image..." -ForegroundColor $Yellow
$backendDockerfile = if ($Production) { "Dockerfile.prod" } else { "Dockerfile" }
$backendImageName = "$Registry/uog-event-backend:$Tag"

try {
    docker build -t $backendImageName -f "backend/$backendDockerfile" ./backend
    Write-Host "✓ Backend image built: $backendImageName" -ForegroundColor $Green
} catch {
    Write-Host "✗ Failed to build backend image" -ForegroundColor $Red
    exit 1
}

# Build frontend image
Write-Host "Building frontend image..." -ForegroundColor $Yellow
$frontendDockerfile = if ($Production) { "Dockerfile.prod" } else { "Dockerfile" }
$frontendImageName = "$Registry/uog-event-frontend:$Tag"

# Set build args for frontend
$buildArgs = @()
if ($Production) {
    $buildArgs += "--build-arg", "REACT_APP_API_BASE_URL=https://event.uog.edu.et/api"
    $buildArgs += "--build-arg", "REACT_APP_BACKEND_URL=https://event.uog.edu.et"
} else {
    $buildArgs += "--build-arg", "REACT_APP_API_BASE_URL=http://localhost:8000/api"
    $buildArgs += "--build-arg", "REACT_APP_BACKEND_URL=http://localhost:8000"
}

try {
    $dockerCommand = @("docker", "build", "-t", $frontendImageName, "-f", "frontend/$frontendDockerfile") + $buildArgs + @("./frontend")
    & $dockerCommand[0] $dockerCommand[1..($dockerCommand.Length-1)]
    Write-Host "✓ Frontend image built: $frontendImageName" -ForegroundColor $Green
} catch {
    Write-Host "✗ Failed to build frontend image" -ForegroundColor $Red
    exit 1
}

# Build nginx image (production only)
if ($Production) {
    Write-Host "Building nginx image..." -ForegroundColor $Yellow
    $nginxImageName = "$Registry/uog-event-nginx:$Tag"
    
    try {
        docker build -t $nginxImageName -f nginx/Dockerfile ./nginx
        Write-Host "✓ Nginx image built: $nginxImageName" -ForegroundColor $Green
    } catch {
        Write-Host "✗ Failed to build nginx image" -ForegroundColor $Red
        exit 1
    }
}

# Push images if requested
if ($Push) {
    Write-Host "Pushing images to registry..." -ForegroundColor $Yellow
    
    try {
        docker push $backendImageName
        Write-Host "✓ Backend image pushed" -ForegroundColor $Green
    } catch {
        Write-Host "✗ Failed to push backend image" -ForegroundColor $Red
    }
    
    try {
        docker push $frontendImageName
        Write-Host "✓ Frontend image pushed" -ForegroundColor $Green
    } catch {
        Write-Host "✗ Failed to push frontend image" -ForegroundColor $Red
    }
    
    if ($Production) {
        try {
            docker push $nginxImageName
            Write-Host "✓ Nginx image pushed" -ForegroundColor $Green
        } catch {
            Write-Host "✗ Failed to push nginx image" -ForegroundColor $Red
        }
    }
}

# Show image sizes
Write-Host "========================================" -ForegroundColor $Blue
Write-Host "Built Images:" -ForegroundColor $Green
Write-Host "========================================" -ForegroundColor $Blue

docker images | Select-String "uog-event"

Write-Host "========================================" -ForegroundColor $Blue
Write-Host "Image build completed!" -ForegroundColor $Green
Write-Host "========================================" -ForegroundColor $Blue

# Show usage instructions
Write-Host "Usage Instructions:" -ForegroundColor $Yellow
Write-Host ""
if ($Production) {
    Write-Host "To deploy to production:" -ForegroundColor $Blue
    Write-Host "  .\deploy-prod.ps1" -ForegroundColor $Blue
} else {
    Write-Host "To deploy locally:" -ForegroundColor $Blue
    Write-Host "  .\deploy-local.ps1" -ForegroundColor $Blue
}
Write-Host ""
Write-Host "To run with Docker Compose:" -ForegroundColor $Blue
Write-Host "  docker-compose -f $composeFile up -d" -ForegroundColor $Blue
Write-Host ""
if ($Push) {
    Write-Host "Images have been pushed to: $Registry" -ForegroundColor $Green
} else {
    Write-Host "To push images to registry:" -ForegroundColor $Blue
    Write-Host "  .\build-images.ps1 -Registry $Registry -Tag $Tag -Push" -ForegroundColor $Blue
}

# Security Settings for Production
# This file contains security configurations that should be applied in production

import os
from decouple import config

# Security Middleware
SECURITY_MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django_ratelimit.middleware.RatelimitMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Security Headers
SECURE_SSL_REDIRECT = config('SECURE_SSL_REDIRECT', default=False, cast=bool)
SECURE_HSTS_SECONDS = config('SECURE_HSTS_SECONDS', default=31536000, cast=int)
SECURE_HSTS_INCLUDE_SUBDOMAINS = config('SECURE_HSTS_INCLUDE_SUBDOMAINS', default=True, cast=bool)
SECURE_HSTS_PRELOAD = config('SECURE_HSTS_PRELOAD', default=True, cast=bool)
SECURE_CONTENT_TYPE_NOSNIFF = config('SECURE_CONTENT_TYPE_NOSNIFF', default=True, cast=bool)
SECURE_BROWSER_XSS_FILTER = config('SECURE_BROWSER_XSS_FILTER', default=True, cast=bool)
X_FRAME_OPTIONS = config('X_FRAME_OPTIONS', default='DENY')

# Session Security
SESSION_COOKIE_SECURE = config('SESSION_COOKIE_SECURE', default=False, cast=bool)
CSRF_COOKIE_SECURE = config('CSRF_COOKIE_SECURE', default=False, cast=bool)
SESSION_COOKIE_HTTPONLY = config('SESSION_COOKIE_HTTPONLY', default=True, cast=bool)
CSRF_COOKIE_HTTPONLY = config('CSRF_COOKIE_HTTPONLY', default=True, cast=bool)
SESSION_COOKIE_SAMESITE = config('SESSION_COOKIE_SAMESITE', default='Lax')
CSRF_COOKIE_SAMESITE = config('CSRF_COOKIE_SAMESITE', default='Lax')

# Session timeout (30 minutes)
SESSION_COOKIE_AGE = config('SESSION_COOKIE_AGE', default=1800, cast=int)
SESSION_EXPIRE_AT_BROWSER_CLOSE = config('SESSION_EXPIRE_AT_BROWSER_CLOSE', default=True, cast=bool)

# Content Security Policy
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = ("'self'", "'unsafe-inline'", "'unsafe-eval'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com")
CSP_STYLE_SRC = ("'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com")
CSP_FONT_SRC = ("'self'", "https://fonts.gstatic.com", "https://cdn.jsdelivr.net")
CSP_IMG_SRC = ("'self'", "data:", "https:", "blob:")
CSP_CONNECT_SRC = ("'self'", config('QR_CODE_BASE_URL', default='http://localhost:8000'))

# File Upload Security
FILE_UPLOAD_MAX_MEMORY_SIZE = config('MAX_UPLOAD_SIZE', default=10485760, cast=int)  # 10MB
DATA_UPLOAD_MAX_MEMORY_SIZE = config('MAX_UPLOAD_SIZE', default=10485760, cast=int)  # 10MB
FILE_UPLOAD_PERMISSIONS = 0o644

# Allowed file types for uploads
ALLOWED_IMAGE_EXTENSIONS = config('ALLOWED_IMAGE_TYPES', default='jpg,jpeg,png,gif,webp').split(',')
ALLOWED_DOCUMENT_EXTENSIONS = ['pdf', 'doc', 'docx', 'txt']

# Rate Limiting Configuration
RATELIMIT_ENABLE = config('RATELIMIT_ENABLE', default=True, cast=bool)
RATELIMIT_USE_CACHE = 'default'

# API Rate Limits
API_RATE_LIMIT = config('API_RATE_LIMIT', default='100/h')  # 100 requests per hour per IP
LOGIN_RATE_LIMIT = config('LOGIN_RATE_LIMIT', default='5/m')  # 5 login attempts per minute per IP
REGISTRATION_RATE_LIMIT = config('REGISTRATION_RATE_LIMIT', default='3/h')  # 3 registrations per hour per IP

# Password Validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Logging Configuration for Security
SECURITY_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'security': {
            'format': '{levelname} {asctime} {name} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'security_file': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/app/logs/security.log',
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'security',
        },
    },
    'loggers': {
        'django.security': {
            'handlers': ['security_file'],
            'level': 'WARNING',
            'propagate': True,
        },
        'django_ratelimit': {
            'handlers': ['security_file'],
            'level': 'WARNING',
            'propagate': True,
        },
    },
}

# Cache Configuration for Rate Limiting
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': config('REDIS_URL', default='redis://redis:6379/0'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'PASSWORD': config('REDIS_PASSWORD', default=None),
        },
        'KEY_PREFIX': 'uog_events',
        'TIMEOUT': config('CACHE_TTL', default=300, cast=int),
    }
}

# Admin Security
ADMIN_URL_PREFIX = config('ADMIN_URL_PREFIX', default='admin')

# Environment-specific security settings
def apply_security_settings(settings_dict):
    """Apply security settings to Django settings"""
    
    # Update middleware
    settings_dict['MIDDLEWARE'] = SECURITY_MIDDLEWARE
    
    # Apply security headers
    settings_dict.update({
        'SECURE_SSL_REDIRECT': SECURE_SSL_REDIRECT,
        'SECURE_HSTS_SECONDS': SECURE_HSTS_SECONDS,
        'SECURE_HSTS_INCLUDE_SUBDOMAINS': SECURE_HSTS_INCLUDE_SUBDOMAINS,
        'SECURE_HSTS_PRELOAD': SECURE_HSTS_PRELOAD,
        'SECURE_CONTENT_TYPE_NOSNIFF': SECURE_CONTENT_TYPE_NOSNIFF,
        'SECURE_BROWSER_XSS_FILTER': SECURE_BROWSER_XSS_FILTER,
        'X_FRAME_OPTIONS': X_FRAME_OPTIONS,
    })
    
    # Apply session security
    settings_dict.update({
        'SESSION_COOKIE_SECURE': SESSION_COOKIE_SECURE,
        'CSRF_COOKIE_SECURE': CSRF_COOKIE_SECURE,
        'SESSION_COOKIE_HTTPONLY': SESSION_COOKIE_HTTPONLY,
        'CSRF_COOKIE_HTTPONLY': CSRF_COOKIE_HTTPONLY,
        'SESSION_COOKIE_SAMESITE': SESSION_COOKIE_SAMESITE,
        'CSRF_COOKIE_SAMESITE': CSRF_COOKIE_SAMESITE,
        'SESSION_COOKIE_AGE': SESSION_COOKIE_AGE,
        'SESSION_EXPIRE_AT_BROWSER_CLOSE': SESSION_EXPIRE_AT_BROWSER_CLOSE,
    })
    
    # Apply file upload security
    settings_dict.update({
        'FILE_UPLOAD_MAX_MEMORY_SIZE': FILE_UPLOAD_MAX_MEMORY_SIZE,
        'DATA_UPLOAD_MAX_MEMORY_SIZE': DATA_UPLOAD_MAX_MEMORY_SIZE,
        'FILE_UPLOAD_PERMISSIONS': FILE_UPLOAD_PERMISSIONS,
    })
    
    # Apply cache configuration
    settings_dict['CACHES'] = CACHES
    
    # Apply password validation
    settings_dict['AUTH_PASSWORD_VALIDATORS'] = AUTH_PASSWORD_VALIDATORS
    
    return settings_dict

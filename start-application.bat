@echo off
echo ========================================
echo UoG Event Management System Startup
echo ========================================
echo.

REM Change to the project directory
cd /d "C:\Users\<USER>\Desktop\uog event"

echo [1/6] Stopping any existing containers...
docker-compose down
echo.

echo [2/6] Building all services...
docker-compose build --no-cache
if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)
echo.

echo [3/6] Starting database and Redis...
docker-compose up -d db redis
echo Waiting for database to be ready...
timeout /t 10 /nobreak > nul
echo.

echo [4/6] Starting backend service...
docker-compose up -d backend
echo Waiting for backend to be ready...
timeout /t 15 /nobreak > nul
echo.

echo [5/6] Testing backend API...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/api/events/' -UseBasicParsing -TimeoutSec 10; Write-Host 'Backend API is responding: HTTP' $response.StatusCode } catch { Write-Host 'Backend API test failed:' $_.Exception.Message }"
echo.

echo [6/6] Starting frontend development server...
echo Opening new terminal for frontend...
start cmd /k "cd /d \"C:\Users\<USER>\Desktop\uog event\frontend\" && npm install && npm start"
echo.

echo ========================================
echo Application Startup Complete!
echo ========================================
echo.
echo Services running:
echo - Backend API: http://localhost:8000
echo - Frontend: http://localhost:3001 (will open automatically)
echo - Database: PostgreSQL on port 5432
echo - Redis: Redis on port 6379
echo.
echo Management URLs:
echo - Admin Panel: http://localhost:8000/admin/
echo - API Documentation: http://localhost:8000/api/
echo.
echo New Features Available:
echo - Hotel Management: http://localhost:3001/hotels
echo - Driver Management: http://localhost:3001/drivers  
echo - Participant Types: http://localhost:3001/participant-types
echo.
echo Press any key to view application status...
pause > nul

echo.
echo Checking service status...
docker-compose ps
echo.

echo Opening application in browser...
timeout /t 3 /nobreak > nul
start http://localhost:3001

echo.
echo ========================================
echo Startup script completed successfully!
echo ========================================
echo.
echo To stop all services, run: docker-compose down
echo To view logs, run: docker-compose logs -f
echo.
pause
